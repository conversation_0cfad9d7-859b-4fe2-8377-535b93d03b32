import { MongoClient } from "mongodb"
import fetch from "node-fetch"

interface RateLimiter {
  requests: number[]
  maxRequests: number
  timeWindow: number
}

interface ScrapedItem {
  name: string
  type: string
  category: string
  rarity?: string
  price?: string
  robuxPrice?: string
  description?: string
  stats?: string[]
  moves?: Array<{
    name: string
    key?: string
    damage?: number
    cooldown?: number
    mastery?: number
    energy?: number
    type?: string
    description?: string
  }>
  imageUrl?: string
  wikiUrl: string
  lastUpdated: Date
  obtainment?: string
  upgrading?: string
  location?: string
  requirements?: string[]
  rewards?: string[]
  hp?: number
  level?: number
  // Nouvelles propriétés pour les matériaux
  materialData?: {
    berryTypes?: string[]
    locations?: Array<{ sea: string; location: string; bushes: number }>
    usage?: Array<{ type: string; items: Array<{ name: string; rarity: string; price: string }> }>
    totalRequired?: Record<string, number>
    maxStack?: number
    source?: string
    spawnRate?: string
    despawnTime?: string
  }
  // Nouvelles propriétés pour les fruits
  fruitData?: {
    awakening?: boolean
    type?: "Natural" | "Elemental" | "Beast" | "Zoan" | "Logia" | "Paramecia"
    value?: number
    stockChance?: number
    spawnChance?: number
    transformation?: boolean
    passiveAbilities?: Array<{ name: string; description: string }>
    masteryRequirements?: Record<string, number>
    combatRating?: {
      pvp?: "Excellent" | "Good" | "Average" | "Poor"
      grinding?: "Excellent" | "Good" | "Average" | "Poor"
      raids?: "Excellent" | "Good" | "Average" | "Poor"
    }
    pros?: string[]
    cons?: string[]
    trivia?: string[]
    recommendations?: string[]
    counters?: string[]
    changeHistory?: Array<{ update: string; changes: string[] }>
  }
  // Nouvelles propriétés pour les armes
  weaponData?: {
    damage?: number
    masteryRequired?: number
    upgradeRequirements?: Array<{ material: string; quantity: number }>
    specialAbilities?: string[]
  }
  // Nouvelles propriétés pour les NPCs
  npcData?: {
    npcType?: "Quest" | "Shop" | "Misc" | "Boss" | "Enemy"
    sea?: number
    location?: string
    questRequirements?: Array<{ type: string; description: string; amount?: number }>
    questRewards?: Array<{ type: string; description: string; amount?: number }>
    questSteps?: string[]
    dialogue?: string[]
    cost?: number
    services?: string[]
  }
  // Nouvelles propriétés pour les quêtes
  questData?: {
    questGiver?: string
    requirements?: Array<{ type: string; description: string; amount?: number }>
    rewards?: Array<{ type: string; description: string; amount?: number }>
    steps?: string[]
    difficulty?: "Easy" | "Medium" | "Hard" | "Extreme"
    estimatedTime?: string
    tips?: string[]
  }
  // Nouvelles propriétés pour les raids/ennemis
  enemyData?: {
    enemyType?: "Raid" | "Boss" | "Regular" | "Elite"
    hp?: number
    level?: number
    baseAttack?: number
    attacks?: Array<{ name: string; description: string; howToAvoid: string }>
    immunity?: string[]
    aura?: boolean
    weapon?: string
    spawnLocation?: string[]
    behavior?: string
  }
  rawData: {
    infobox?: Record<string, string>
    movesRaw?: string
    descriptionRaw?: string
    fullWikitextSample?: string // Added for debugging
    wikitextLength: number
    movesFound: number
    statsFound: number
    extractedAt: string
    materialData?: any
    fruitData?: any
    weaponData?: any
    npcData?: any
    questData?: any
    enemyData?: any
  }
}

export class FandomAPIScraper {
  private baseUrl = "https://blox-fruits.fandom.com/api.php"
  private mongoClient: MongoClient
  private rateLimiter: RateLimiter

  constructor(mongoUrl: string) {
    this.mongoClient = new MongoClient(mongoUrl)
    this.rateLimiter = {
      requests: [],
      maxRequests: 90, // 90 requests per minute to be safe
      timeWindow: 60000, // 1 minute
    }
  }

  private async waitForRateLimit(): Promise<void> {
    const now = Date.now()
    this.rateLimiter.requests = this.rateLimiter.requests.filter((time) => now - time < this.rateLimiter.timeWindow)

    if (this.rateLimiter.requests.length >= this.rateLimiter.maxRequests) {
      const waitTime = this.rateLimiter.timeWindow - (now - this.rateLimiter.requests[0]) + 1000 // Add 1s buffer
      console.log(`⏳ Rate limit reached, waiting ${Math.ceil(waitTime / 1000)}s...`)
      await new Promise((resolve) => setTimeout(resolve, waitTime))
    }

    this.rateLimiter.requests.push(now)
  }

  private async makeApiRequest(params: Record<string, string>): Promise<any> {
    await this.waitForRateLimit()

    const url = `${this.baseUrl}?${new URLSearchParams(params)}`
    // console.log(`Fetching: ${url}`); // Log URL for debugging

    const response = await fetch(url)

    if (!response.ok) {
      throw new Error(`API request failed: ${response.status} ${response.statusText} for ${url}`)
    }

    return await response.json()
  }

  async getCategoryMembers(categoryName: string): Promise<Array<{ title: string; pageid: number }>> {
    console.log(`🔍 Fetching members of category: ${categoryName}`)

    const params = {
      action: "query",
      list: "categorymembers",
      cmtitle: `Category:${categoryName}`,
      cmlimit: "500", // Max limit
      format: "json",
    }

    try {
      const data = await this.makeApiRequest(params)

      if (!data.query?.categorymembers) {
        console.warn(`⚠️ No members found for category: ${categoryName}`)
        return []
      }

      const members = data.query.categorymembers
      console.log(`✅ Found ${members.length} members in ${categoryName}`)
      return members
    } catch (error) {
      console.error(`❌ Error fetching category ${categoryName}:`, error)
      return []
    }
  }

  async getPageContent(title: string): Promise<string | null> {
    const params = {
      action: "query",
      titles: title,
      prop: "revisions",
      rvprop: "content",
      format: "json",
    }

    try {
      const data = await this.makeApiRequest(params)
      const pages = data.query?.pages

      if (!pages) return null

      const pageId = Object.keys(pages)[0]
      const page = pages[pageId]

      if (page.missing || !page.revisions) {
        // Handle redirects: if it's a redirect, try to get content from the target page
        if (page.revisions?.[0]?.["*"]?.startsWith("#REDIRECT")) {
          const redirectTargetMatch = page.revisions[0]["*"].match(/#REDIRECT\s*\[\[([^\]]+)\]\]/i)
          if (redirectTargetMatch && redirectTargetMatch[1]) {
            console.log(`🔄 Redirect found for "${title}", redirecting to "${redirectTargetMatch[1]}"`)
            return this.getPageContent(redirectTargetMatch[1]) // Recursively get content from target
          }
        }
        return null
      }

      return page.revisions[0]["*"]
    } catch (error) {
      console.error(`❌ Error fetching content for ${title}:`, error)
      return null
    }
  }

  private extractTemplateData(wikitext: string, templateName: string): Record<string, string> {
    const data: Record<string, string> = {}

    // Pattern for finding the template specifically, more robust
    // Handles templates like {{TemplateName|param1=value1|param2=value2}}
    const templatePattern = new RegExp(`\\{\\{\\s*${templateName}\\s*\\|([\\s\\S]*?)\\}\\}`, "i")
    const match = wikitext.match(templatePattern)

    if (!match) {
      return data
    }

    const templateContent = match[1]

    // Extract parameters from the template
    // This regex is designed to capture key=value pairs, handling newlines and internal pipes
    const paramPattern = /\|\s*([^=|]+)\s*=\s*([\s\S]*?)(?=\|\s*\w+\s*=|\|\s*\}\}|\n\}\})/g
    let paramMatch

    while ((paramMatch = paramPattern.exec(templateContent)) !== null) {
      const key = paramMatch[1].trim().toLowerCase()
      const value = paramMatch[2].trim()

      if (value && value !== "") {
        data[key] = this.cleanWikitext(value)
      }
    }

    return data
  }

  // Nouvelle méthode pour extraire les données spécifiques aux matériaux
  private extractMaterialData(wikitext: string): {
    berryTypes?: string[]
    locations?: Array<{ sea: string; location: string; bushes: number }>
    usage?: Array<{ type: string; items: Array<{ name: string; rarity: string; price: string }> }>
    totalRequired?: Record<string, number>
    maxStack?: number
    source?: string
    spawnRate?: string
    despawnTime?: string
  } {
    const materialData: any = {}

    // Extraire la liste des types de berries
    const berryListMatch = wikitext.match(/==\s*List of Berries\s*==([\s\S]*?)(?===|$)/i)
    if (berryListMatch) {
      const berryTypes: string[] = []
      const materialMatches = berryListMatch[1].match(/\{\{Material\|([^}]+)\}\}/g)
      if (materialMatches) {
        materialMatches.forEach(match => {
          const berryName = match.replace(/\{\{Material\|([^}]+)\}\}/, '$1').trim()
          berryTypes.push(berryName)
        })
      }
      materialData.berryTypes = berryTypes
    }

    // Extraire les locations avec le nombre de bushes
    const locationsMatch = wikitext.match(/\{\|class="sortable mw-collapsible fandom-table"[\s\S]*?\|\}/i)
    if (locationsMatch) {
      const locations: Array<{ sea: string; location: string; bushes: number }> = []
      const tableContent = locationsMatch[0]

      // Extraire les lignes du tableau
      const rows = tableContent.split('|-').slice(1) // Ignorer l'en-tête

      rows.forEach(row => {
        const cells = row.split('|').map(cell => cell.trim()).filter(cell => cell && !cell.startsWith('{'))

        if (cells.length >= 6) {
          // First Sea
          if (cells[0] && cells[1]) {
            const bushCount = parseInt(cells[1])
            if (!isNaN(bushCount)) {
              locations.push({
                sea: "First Sea",
                location: this.cleanWikitext(cells[0]),
                bushes: bushCount
              })
            }
          }

          // Second Sea
          if (cells[2] && cells[3]) {
            const bushCount = parseInt(cells[3])
            if (!isNaN(bushCount)) {
              locations.push({
                sea: "Second Sea",
                location: this.cleanWikitext(cells[2]),
                bushes: bushCount
              })
            }
          }

          // Third Sea
          if (cells[4] && cells[5]) {
            const bushCount = parseInt(cells[5])
            if (!isNaN(bushCount)) {
              locations.push({
                sea: "Third Sea",
                location: this.cleanWikitext(cells[4]),
                bushes: bushCount
              })
            }
          }
        }
      })

      materialData.locations = locations
    }

    // Extraire les informations d'usage (skins, etc.)
    const usageMatch = wikitext.match(/==\s*Usage\s*==([\s\S]*?)(?===|$)/i)
    if (usageMatch) {
      const usage: Array<{ type: string; items: Array<{ name: string; rarity: string; price: string }> }> = []

      // Extraire les sections tabber (Aura Skin, Dragon Skin, Eagle Skin)
      const tabberMatches = usageMatch[1].match(/<tabber>([\s\S]*?)<\/tabber>/i)
      if (tabberMatches) {
        const tabberContent = tabberMatches[1]

        // Extraire chaque section
        const sections = tabberContent.split('|-|')

        sections.forEach(section => {
          const sectionMatch = section.match(/^([^=]+)=([\s\S]*)/)
          if (sectionMatch) {
            const sectionType = sectionMatch[1].trim()
            const sectionContent = sectionMatch[2]

            const items: Array<{ name: string; rarity: string; price: string }> = []

            // Extraire les lignes du tableau de cette section
            const tableMatch = sectionContent.match(/\{\|[\s\S]*?\|\}/)
            if (tableMatch) {
              const rows = tableMatch[0].split('|-').slice(1)

              rows.forEach(row => {
                const cells = row.split('|').map(cell => cell.trim()).filter(cell => cell && !cell.startsWith('{'))
                if (cells.length >= 3) {
                  items.push({
                    name: this.cleanWikitext(cells[0]),
                    rarity: this.cleanWikitext(cells[1]),
                    price: this.cleanWikitext(cells[2])
                  })
                }
              })
            }

            if (items.length > 0) {
              usage.push({
                type: sectionType,
                items
              })
            }
          }
        })
      }

      materialData.usage = usage
    }

    // Extraire les totaux requis
    const totalRequiredMatch = wikitext.match(/It costs a total of 191 Berries to buy everything[\s\S]*?(\{\{[^}]+\}\}[\s\S]*?)(?=\n\n|==)/i)
    if (totalRequiredMatch) {
      const totalRequired: Record<string, number> = {}
      const berryMatches = totalRequiredMatch[1].match(/\{\{([^|]+)\|(\d+)\}\}/g)

      if (berryMatches) {
        berryMatches.forEach(match => {
          const berryMatch = match.match(/\{\{([^|]+)\|(\d+)\}\}/)
          if (berryMatch) {
            const berryName = berryMatch[1].trim()
            const quantity = parseInt(berryMatch[2])
            if (!isNaN(quantity)) {
              totalRequired[berryName] = quantity
            }
          }
        })
      }

      materialData.totalRequired = totalRequired
    }

    return materialData
  }

  // Nouvelle méthode pour extraire les données spécifiques aux fruits
  private extractFruitData(wikitext: string, infoboxData: Record<string, string>): {
    awakening?: boolean
    type?: "Natural" | "Elemental" | "Beast" | "Zoan" | "Logia" | "Paramecia"
    value?: number
    stockChance?: number
    spawnChance?: number
    transformation?: boolean
    passiveAbilities?: Array<{ name: string; description: string }>
    masteryRequirements?: Record<string, number>
    combatRating?: {
      pvp?: "Excellent" | "Good" | "Average" | "Poor"
      grinding?: "Excellent" | "Good" | "Average" | "Poor"
      raids?: "Excellent" | "Good" | "Average" | "Poor"
    }
    pros?: string[]
    cons?: string[]
    trivia?: string[]
    recommendations?: string[]
    counters?: string[]
    changeHistory?: Array<{ update: string; changes: string[] }>
  } {
    const fruitData: any = {}

    // Extraire le type de fruit avec validation
    if (infoboxData.type) {
      const validTypes = ["Natural", "Elemental", "Beast", "Zoan", "Logia", "Paramecia"]
      const type = infoboxData.type.trim()
      if (validTypes.includes(type)) {
        fruitData.type = type as "Natural" | "Elemental" | "Beast" | "Zoan" | "Logia" | "Paramecia"
      }
    }

    // Extraire les informations d'awakening
    if (infoboxData.awakening) {
      fruitData.awakening = infoboxData.awakening.toLowerCase() === 'yes' || infoboxData.awakening.toLowerCase() === 'available'
    }

    // Extraire la valeur du fruit
    if (infoboxData.value || infoboxData.money || infoboxData.price) {
      const valueStr = infoboxData.value || infoboxData.money || infoboxData.price
      const value = this.parseNumber(valueStr)
      if (value) fruitData.value = value
    }

    // Extraire les chances de spawn et stock
    if (infoboxData.stock_chance || infoboxData.stockchance) {
      const stockChance = this.parseNumber(infoboxData.stock_chance || infoboxData.stockchance)
      if (stockChance) fruitData.stockChance = stockChance
    }

    if (infoboxData.spawn_chance || infoboxData.spawnchance) {
      const spawnChance = this.parseNumber(infoboxData.spawn_chance || infoboxData.spawnchance)
      if (spawnChance) fruitData.spawnChance = spawnChance
    }

    // Détecter la transformation
    if (wikitext.toLowerCase().includes("transformation") || wikitext.toLowerCase().includes("transform")) {
      fruitData.transformation = true
    }

    // Extraire les capacités passives
    const passiveMatch = wikitext.match(/Passive=([\s\S]*?)(?=\|-\||<\/tabber>)/i)
    if (passiveMatch) {
      const passiveAbilities: Array<{ name: string; description: string }> = []
      const passiveContent = passiveMatch[1]

      // Extraire les lignes du tableau des passives
      const rows = passiveContent.split('|-').slice(1)

      rows.forEach(row => {
        const cells = row.split('|').map(cell => cell.trim()).filter(cell => cell && !cell.startsWith('{'))
        if (cells.length >= 2) {
          const name = this.cleanWikitext(cells[0])
          const description = this.cleanWikitext(cells[1])
          if (name && description && name.length > 2 && description.length > 10) {
            passiveAbilities.push({ name, description })
          }
        }
      })

      if (passiveAbilities.length > 0) {
        fruitData.passiveAbilities = passiveAbilities
      }
    }

    // Extraire les exigences de maîtrise depuis les moves
    const masteryRequirements: Record<string, number> = {}
    const skillBoxMatches = wikitext.match(/\{\{SkillBox\|[^}]*Mas\s*=\s*(\d+)[^}]*Move\s*=\s*([^|}]+)/g)
    if (skillBoxMatches) {
      skillBoxMatches.forEach(match => {
        const masteryMatch = match.match(/Mas\s*=\s*(\d+)/)
        const moveMatch = match.match(/Move\s*=\s*([^|}]+)/)
        if (masteryMatch && moveMatch) {
          const mastery = parseInt(masteryMatch[1])
          const moveName = this.cleanWikitext(moveMatch[1])
          if (!isNaN(mastery) && moveName) {
            masteryRequirements[moveName] = mastery
          }
        }
      })
    }

    if (Object.keys(masteryRequirements).length > 0) {
      fruitData.masteryRequirements = masteryRequirements
    }

    // Extraire les pros et cons
    const prosMatch = wikitext.match(/Pros=[\s\S]*?Normal=([\s\S]*?)(?=\{\{\!\}\}-\{\{\!\}\}|Transformed=)/i)
    if (prosMatch) {
      const pros: string[] = []
      const prosContent = prosMatch[1]
      const prosLines = prosContent.split('\n').filter(line => line.trim().startsWith('*'))

      prosLines.forEach(line => {
        const cleanLine = this.cleanWikitext(line.replace('*', '').trim())
        if (cleanLine.length > 10) {
          pros.push(cleanLine)
        }
      })

      if (pros.length > 0) {
        fruitData.pros = pros.slice(0, 10) // Limiter à 10 pros
      }
    }

    const consMatch = wikitext.match(/Cons=[\s\S]*?Normal=([\s\S]*?)(?=\{\{\!\}\}-\{\{\!\}\}|Transformed=)/i)
    if (consMatch) {
      const cons: string[] = []
      const consContent = consMatch[1]
      const consLines = consContent.split('\n').filter(line => line.trim().startsWith('*'))

      consLines.forEach(line => {
        const cleanLine = this.cleanWikitext(line.replace('*', '').trim())
        if (cleanLine.length > 10) {
          cons.push(cleanLine)
        }
      })

      if (cons.length > 0) {
        fruitData.cons = cons.slice(0, 10) // Limiter à 10 cons
      }
    }

    // Évaluer les ratings de combat basés sur le contenu
    const combatRating: any = {}

    // PvP rating
    if (wikitext.toLowerCase().includes("very good option for pvp") ||
        wikitext.toLowerCase().includes("excellent for pvp")) {
      combatRating.pvp = "Excellent"
    } else if (wikitext.toLowerCase().includes("good for pvp")) {
      combatRating.pvp = "Good"
    }

    // Grinding rating
    if (wikitext.toLowerCase().includes("very good for grinding") ||
        wikitext.toLowerCase().includes("excellent for grinding")) {
      combatRating.grinding = "Excellent"
    } else if (wikitext.toLowerCase().includes("good for grinding")) {
      combatRating.grinding = "Good"
    } else if (wikitext.toLowerCase().includes("decent for grinding")) {
      combatRating.grinding = "Average"
    }

    // Raids rating
    if (wikitext.toLowerCase().includes("extremely good for raids")) {
      combatRating.raids = "Excellent"
    } else if (wikitext.toLowerCase().includes("good for raids")) {
      combatRating.raids = "Good"
    }

    if (Object.keys(combatRating).length > 0) {
      fruitData.combatRating = combatRating
    }

    // Extraire les informations de trivia
    const triviaMatch = wikitext.match(/==\s*Trivia\s*==([\s\S]*?)(?===|$)/i)
    if (triviaMatch) {
      const trivia: string[] = []
      const triviaContent = triviaMatch[1]
      const triviaLines = triviaContent.split('\n').filter(line => line.trim().startsWith('*'))

      triviaLines.forEach(line => {
        const cleanLine = this.cleanWikitext(line.replace('*', '').trim())
        if (cleanLine.length > 15 && !cleanLine.toLowerCase().includes('category:')) {
          trivia.push(cleanLine)
        }
      })

      if (trivia.length > 0) {
        fruitData.trivia = trivia.slice(0, 15) // Limiter à 15 trivia
      }
    }

    // Extraire les recommandations de races/styles
    const recommendations: string[] = []
    const raceRecommendations = wikitext.match(/It is recommended to use \[\[([^\]]+)\]\]/gi)
    if (raceRecommendations) {
      raceRecommendations.forEach(match => {
        const raceMatch = match.match(/\[\[([^\]]+)\]\]/)
        if (raceMatch) {
          const race = this.cleanWikitext(raceMatch[1])
          if (race && !recommendations.includes(race)) {
            recommendations.push(race)
          }
        }
      })
    }

    // Extraire les recommandations générales
    const generalRecommendations = wikitext.match(/recommended[^.]*\./gi)
    if (generalRecommendations) {
      generalRecommendations.forEach(rec => {
        const cleanRec = this.cleanWikitext(rec)
        if (cleanRec.length > 20 && cleanRec.length < 200) {
          recommendations.push(cleanRec)
        }
      })
    }

    if (recommendations.length > 0) {
      fruitData.recommendations = [...new Set(recommendations)].slice(0, 8) // Remove duplicates, limit to 8
    }

    // Extraire les informations de counter
    const counters: string[] = []
    const counterMatches = wikitext.match(/good counter to[^.]*\./gi)
    if (counterMatches) {
      counterMatches.forEach(counter => {
        const cleanCounter = this.cleanWikitext(counter)
        if (cleanCounter.length > 10) {
          counters.push(cleanCounter)
        }
      })
    }

    // Extraire les fruits mentionnés comme counters
    const fruitCounters = wikitext.match(/counter to fruits like \[\[([^\]]+)\]\]/gi)
    if (fruitCounters) {
      fruitCounters.forEach(match => {
        const fruits = match.match(/\[\[([^\]]+)\]\]/g)
        if (fruits) {
          fruits.forEach(fruit => {
            const fruitName = fruit.replace(/\[\[|\]\]/g, '')
            counters.push(`Counters ${fruitName}`)
          })
        }
      })
    }

    if (counters.length > 0) {
      fruitData.counters = [...new Set(counters)].slice(0, 5) // Remove duplicates, limit to 5
    }

    // Extraire l'historique des changements
    const changeHistoryMatch = wikitext.match(/==\s*Change History\s*==([\s\S]*?)(?===|$)/i)
    if (changeHistoryMatch) {
      const changeHistory: Array<{ update: string; changes: string[] }> = []
      const historyContent = changeHistoryMatch[1]

      // Extraire les updates
      const updateMatches = historyContent.match(/\{\{Update\|([^}]+)\}\}([\s\S]*?)(?=\{\{Update\||----|\}\})/g)
      if (updateMatches) {
        updateMatches.forEach(updateBlock => {
          const updateMatch = updateBlock.match(/\{\{Update\|([^}]+)\}\}/)
          if (updateMatch) {
            const updateNumber = updateMatch[1].trim()
            const changes: string[] = []

            // Extraire les changements
            const changeLines = updateBlock.split('\n').filter(line => line.trim().startsWith('*'))
            changeLines.forEach(line => {
              const cleanChange = this.cleanWikitext(line.replace('*', '').trim())
              if (cleanChange.length > 5) {
                changes.push(cleanChange)
              }
            })

            if (changes.length > 0) {
              changeHistory.push({
                update: updateNumber,
                changes: changes
              })
            }
          }
        })
      }

      if (changeHistory.length > 0) {
        fruitData.changeHistory = changeHistory
      }
    }

    // Chercher des informations additionnelles dans le texte
    const valueMatch = wikitext.match(/(?:worth|value|costs?)\s*(?:of\s*)?(?:approximately\s*)?[\$₿]?([\d,]+)/i)
    if (valueMatch && !fruitData.value) {
      const value = this.parseNumber(valueMatch[1])
      if (value) fruitData.value = value
    }

    return fruitData
  }

  // Nouvelle méthode pour extraire les données spécifiques aux armes
  private extractWeaponData(wikitext: string, infoboxData: Record<string, string>): {
    damage?: number
    masteryRequired?: number
    upgradeRequirements?: Array<{ material: string; quantity: number }>
    specialAbilities?: string[]
  } {
    const weaponData: any = {}

    // Extraire les dégâts
    if (infoboxData.damage) {
      const damage = this.parseNumber(infoboxData.damage)
      if (damage) weaponData.damage = damage
    }

    // Extraire la maîtrise requise
    if (infoboxData.mastery || infoboxData.mastery_required || infoboxData.masteryreq) {
      const mastery = this.parseNumber(infoboxData.mastery || infoboxData.mastery_required || infoboxData.masteryreq)
      if (mastery) weaponData.masteryRequired = mastery
    }

    // Extraire les exigences d'amélioration
    const upgradeMatch = wikitext.match(/==\s*Upgrading\s*==([\s\S]*?)(?===|$)/i)
    if (upgradeMatch) {
      const upgradeRequirements: Array<{ material: string; quantity: number }> = []

      // Chercher les patterns de matériaux requis
      const materialMatches = upgradeMatch[1].match(/\{\{([^|]+)\|(\d+)\}\}/g)
      if (materialMatches) {
        materialMatches.forEach(match => {
          const materialMatch = match.match(/\{\{([^|]+)\|(\d+)\}\}/)
          if (materialMatch) {
            const material = materialMatch[1].trim()
            const quantity = parseInt(materialMatch[2])
            if (!isNaN(quantity)) {
              upgradeRequirements.push({ material, quantity })
            }
          }
        })
      }

      if (upgradeRequirements.length > 0) {
        weaponData.upgradeRequirements = upgradeRequirements
      }
    }

    // Extraire les capacités spéciales
    const specialAbilities: string[] = []

    // Chercher dans les sections communes
    const abilityPatterns = [
      /==\s*Special\s*==([\s\S]*?)(?===|$)/i,
      /==\s*Abilities\s*==([\s\S]*?)(?===|$)/i,
      /==\s*Passive\s*==([\s\S]*?)(?===|$)/i
    ]

    abilityPatterns.forEach(pattern => {
      const match = wikitext.match(pattern)
      if (match) {
        const abilities = match[1]
          .split('\n')
          .map(line => this.cleanWikitext(line.trim()))
          .filter(line => line.length > 10 && !line.startsWith('{{') && !line.startsWith('=='))

        specialAbilities.push(...abilities)
      }
    })

    if (specialAbilities.length > 0) {
      weaponData.specialAbilities = [...new Set(specialAbilities)] // Remove duplicates
    }

    return weaponData
  }

  // Nouvelle méthode pour extraire les données spécifiques aux NPCs
  private extractNPCData(wikitext: string, infoboxData: Record<string, string>): {
    npcType?: "Quest" | "Shop" | "Misc" | "Boss" | "Enemy"
    sea?: number
    location?: string
    questRequirements?: Array<{ type: string; description: string; amount?: number }>
    questRewards?: Array<{ type: string; description: string; amount?: number }>
    questSteps?: string[]
    dialogue?: string[]
    cost?: number
    services?: string[]
  } {
    const npcData: any = {}

    // Extraire le type de NPC
    if (infoboxData.type) {
      const type = infoboxData.type.trim()
      if (["Quest", "Shop", "Misc", "Boss", "Enemy"].includes(type)) {
        npcData.npcType = type as "Quest" | "Shop" | "Misc" | "Boss" | "Enemy"
      }
    }

    // Extraire la mer et la localisation
    if (infoboxData.sea) {
      const sea = this.parseNumber(infoboxData.sea)
      if (sea) npcData.sea = sea
    }

    if (infoboxData.location) {
      npcData.location = infoboxData.location
    }

    // Extraire les exigences de quête
    const requirementsMatch = wikitext.match(/==\s*Requirements\s*==([\s\S]*?)(?===|$)/i)
    if (requirementsMatch) {
      const requirements: Array<{ type: string; description: string; amount?: number }> = []
      const reqContent = requirementsMatch[1]

      // Chercher les patterns de requirements
      const reqLines = reqContent.split('\n').filter(line => line.trim().startsWith('*'))

      reqLines.forEach(line => {
        const cleanLine = this.cleanWikitext(line.replace('*', '').trim())
        if (cleanLine.length > 5) {
          // Extraire les montants monétaires
          const moneyMatch = cleanLine.match(/\{\{Money\|([^}]+)\}\}/)
          if (moneyMatch) {
            const amount = this.parseNumber(moneyMatch[1])
            requirements.push({
              type: "Money",
              description: cleanLine,
              amount: amount
            })
          } else {
            requirements.push({
              type: "General",
              description: cleanLine
            })
          }
        }
      })

      if (requirements.length > 0) {
        npcData.questRequirements = requirements
      }
    }

    // Extraire les étapes de quête depuis les tableaux
    const questTableMatch = wikitext.match(/\{\|\s*class="fandom-table"[\s\S]*?\|\}/i)
    if (questTableMatch) {
      const questSteps: string[] = []
      const tableContent = questTableMatch[0]

      // Extraire les lignes du tableau
      const rows = tableContent.split('|-').slice(1)

      rows.forEach(row => {
        const cells = row.split('|').map(cell => cell.trim()).filter(cell => cell && !cell.startsWith('{'))
        if (cells.length >= 2) {
          const race = this.cleanWikitext(cells[0])
          const quest = this.cleanWikitext(cells[1])
          if (race && quest && quest.length > 10) {
            questSteps.push(`${race}: ${quest}`)
          }
        }
      })

      if (questSteps.length > 0) {
        npcData.questSteps = questSteps
      }
    }

    // Extraire le dialogue
    const dialogue: string[] = []
    const dialoguePatterns = [
      /"([^"]+)"/g,
      /''([^']+)''/g
    ]

    dialoguePatterns.forEach(pattern => {
      let match
      while ((match = pattern.exec(wikitext)) !== null) {
        const dialogueLine = this.cleanWikitext(match[1])
        if (dialogueLine.length > 5 && dialogueLine.length < 200) {
          dialogue.push(dialogueLine)
        }
      }
    })

    if (dialogue.length > 0) {
      npcData.dialogue = [...new Set(dialogue)].slice(0, 10) // Remove duplicates, limit to 10
    }

    // Extraire les services
    const services: string[] = []
    if (wikitext.toLowerCase().includes('evolve') || wikitext.toLowerCase().includes('upgrade')) {
      services.push("Race Evolution")
    }
    if (wikitext.toLowerCase().includes('shop') || wikitext.toLowerCase().includes('buy')) {
      services.push("Shop")
    }
    if (wikitext.toLowerCase().includes('quest')) {
      services.push("Quest Giver")
    }

    if (services.length > 0) {
      npcData.services = services
    }

    return npcData
  }

  // Nouvelle méthode pour extraire les données spécifiques aux quêtes
  private extractQuestData(wikitext: string, infoboxData: Record<string, string>): {
    questGiver?: string
    requirements?: Array<{ type: string; description: string; amount?: number }>
    rewards?: Array<{ type: string; description: string; amount?: number }>
    steps?: string[]
    difficulty?: "Easy" | "Medium" | "Hard" | "Extreme"
    estimatedTime?: string
    tips?: string[]
  } {
    const questData: any = {}

    // Extraire le quest giver depuis le titre ou le contenu
    const questGiverMatch = wikitext.match(/'''([^']+)'''\s+is\s+an?\s+\[\[NPC\]\]/)
    if (questGiverMatch) {
      questData.questGiver = questGiverMatch[1]
    }

    // Extraire les requirements
    const requirementsMatch = wikitext.match(/===?\s*Requirements\s*===?([\s\S]*?)(?===|$)/i)
    if (requirementsMatch) {
      const requirements: Array<{ type: string; description: string; amount?: number }> = []
      const reqContent = requirementsMatch[1]

      const reqLines = reqContent.split('\n').filter(line => line.trim().startsWith('*'))

      reqLines.forEach(line => {
        const cleanLine = this.cleanWikitext(line.replace('*', '').trim())
        if (cleanLine.length > 5) {
          // Extraire les montants
          const moneyMatch = cleanLine.match(/\{\{Money\|([^}]+)\}\}/)
          const fragmentMatch = cleanLine.match(/\{\{Fragment\|([^}]+)\}\}/)

          if (moneyMatch) {
            const amount = this.parseNumber(moneyMatch[1])
            requirements.push({
              type: "Money",
              description: cleanLine,
              amount: amount
            })
          } else if (fragmentMatch) {
            const amount = this.parseNumber(fragmentMatch[1])
            requirements.push({
              type: "Fragment",
              description: cleanLine,
              amount: amount
            })
          } else {
            requirements.push({
              type: "General",
              description: cleanLine
            })
          }
        }
      })

      if (requirements.length > 0) {
        questData.requirements = requirements
      }
    }

    // Extraire les étapes de la quête
    const flowerQuestMatch = wikitext.match(/===?\s*Flower Quest\s*===?([\s\S]*?)(?===|$)/i)
    if (flowerQuestMatch) {
      const steps: string[] = []
      const stepContent = flowerQuestMatch[1]

      const stepLines = stepContent.split('\n').filter(line => line.trim().startsWith('*'))

      stepLines.forEach(line => {
        const cleanLine = this.cleanWikitext(line.replace('*', '').trim())
        if (cleanLine.length > 10) {
          steps.push(cleanLine)
        }
      })

      if (steps.length > 0) {
        questData.steps = steps
      }
    }

    // Extraire les tips
    const tips: string[] = []
    const tipMatches = wikitext.match(/\*?\s*Tip:([^*\n]+)/gi)
    if (tipMatches) {
      tipMatches.forEach(tip => {
        const cleanTip = this.cleanWikitext(tip.replace(/\*?\s*Tip:\s*/i, '').trim())
        if (cleanTip.length > 10) {
          tips.push(cleanTip)
        }
      })
    }

    if (tips.length > 0) {
      questData.tips = tips
    }

    // Estimer la difficulté basée sur les requirements
    if (questData.requirements) {
      const hasMoneyReq = questData.requirements.some((req: any) => req.type === "Money" && req.amount && req.amount > 1000000)
      const hasComplexReq = questData.requirements.some((req: any) => req.description.toLowerCase().includes("defeat") || req.description.toLowerCase().includes("kill"))

      if (hasMoneyReq && hasComplexReq) {
        questData.difficulty = "Hard"
      } else if (hasMoneyReq || hasComplexReq) {
        questData.difficulty = "Medium"
      } else {
        questData.difficulty = "Easy"
      }
    }

    return questData
  }

  // Nouvelle méthode pour extraire les données spécifiques aux ennemis/raids
  private extractEnemyData(wikitext: string, infoboxData: Record<string, string>): {
    enemyType?: "Raid" | "Boss" | "Regular" | "Elite"
    hp?: number
    level?: number
    baseAttack?: number
    attacks?: Array<{ name: string; description: string; howToAvoid: string }>
    immunity?: string[]
    aura?: boolean
    weapon?: string
    spawnLocation?: string[]
    behavior?: string
  } {
    const enemyData: any = {}

    // Extraire le type d'ennemi
    if (infoboxData.type) {
      const type = infoboxData.type.trim()
      if (type === "Enemy") {
        // Déterminer le sous-type basé sur le contenu
        if (wikitext.toLowerCase().includes("raid")) {
          enemyData.enemyType = "Raid"
        } else if (wikitext.toLowerCase().includes("boss")) {
          enemyData.enemyType = "Boss"
        } else {
          enemyData.enemyType = "Regular"
        }
      }
    }

    // Extraire les stats de base
    if (infoboxData.hp) {
      const hp = this.parseNumber(infoboxData.hp)
      if (hp) enemyData.hp = hp
    }

    if (infoboxData.level) {
      const level = this.parseNumber(infoboxData.level)
      if (level) enemyData.level = level
    }

    if (infoboxData.baseatk || infoboxData.baseattack) {
      const baseAttack = this.parseNumber(infoboxData.baseatk || infoboxData.baseattack)
      if (baseAttack) enemyData.baseAttack = baseAttack
    }

    // Extraire l'aura
    if (infoboxData.aura) {
      enemyData.aura = infoboxData.aura.toLowerCase() === "yes"
    }

    // Extraire l'arme
    if (infoboxData.weapon && infoboxData.weapon !== "None") {
      enemyData.weapon = infoboxData.weapon
    }

    // Extraire les immunités
    if (infoboxData.immunity && infoboxData.immunity !== "N/A") {
      enemyData.immunity = [infoboxData.immunity]
    }

    // Extraire les localisations de spawn
    if (infoboxData.location) {
      enemyData.spawnLocation = [infoboxData.location]
    }

    // Extraire les attaques depuis les tableaux de comportement
    const behaviorMatch = wikitext.match(/==\s*Behavior\s*==([\s\S]*?)(?===|$)/i)
    if (behaviorMatch) {
      const attacks: Array<{ name: string; description: string; howToAvoid: string }> = []
      const behaviorContent = behaviorMatch[1]

      // Chercher les tableaux d'attaques
      const tableMatch = behaviorContent.match(/\{\|\s*class="fandom-table"[\s\S]*?\|\}/)
      if (tableMatch) {
        const tableContent = tableMatch[0]
        const rows = tableContent.split('|-').slice(1) // Ignorer l'en-tête

        rows.forEach(row => {
          const cells = row.split('|').map(cell => cell.trim()).filter(cell => cell && !cell.startsWith('{') && !cell.startsWith('!'))

          if (cells.length >= 3) {
            const attackName = this.cleanWikitext(cells[0])
            const description = this.cleanWikitext(cells[1])
            const howToAvoid = this.cleanWikitext(cells[2])

            if (attackName && description && howToAvoid) {
              attacks.push({
                name: attackName,
                description: description,
                howToAvoid: howToAvoid
              })
            }
          }
        })
      }

      if (attacks.length > 0) {
        enemyData.attacks = attacks
      }

      // Extraire le comportement général
      const behaviorLines = behaviorContent.split('\n')
        .filter(line => !line.includes('|') && !line.includes('{') && line.trim().length > 20)
        .map(line => this.cleanWikitext(line.trim()))
        .filter(line => line.length > 10)

      if (behaviorLines.length > 0) {
        enemyData.behavior = behaviorLines[0]
      }
    }

    return enemyData
  }

  private cleanWikitext(text: string): string {
    return text
      .replace(/\[\[(?:[^|\]]+\|)?([^\]]+)\]\]/g, "$1") // Remove wiki links, keep display text
      .replace(/'''([^']+)'''/g, "$1") // Remove bold
      .replace(/''([^']+)''/g, "$1") // Remove italic
      .replace(
        /\{\{(?:SkillBox|Stats Table|Stats Table Row|Overview|Money|Robux|Fragment|Skin|Ability|CHROMATIC|Update|Change History|Blox Fruit Navbox|CombosTab|About|Enemy Infobox|SkillStart|SkillEnd|#tag:tabber|File|Gallery|Image|Video|Audio|Map|Location|NPC|Quest|Raid|Material|Weapon|Item|Armor|Accessory|Gun|Sword|Fruit|BloxFruit|Devil Fruit|Infobox)[^}]*\}\}/gi,
        "",
      ) // Remove common templates
      .replace(/<!--[\s\S]*?-->/g, "") // Remove comments
      .replace(/<ref[\s\S]*?<\/ref>/g, "") // Remove references
      .replace(/<nowiki>[\s\S]*?<\/nowiki>/g, "") // Remove nowiki tags
      .replace(/<br\s*\/?>/gi, "\n") // Convert <br> to newline
      .replace(/\{\|[\s\S]*?\|\}/g, "") // Remove full tables (including fandom-table)
      .replace(/<gallery>[\s\S]*?<\/gallery>/gi, "") // Remove gallery tags
      .replace(/<center>[\s\S]*?<\/center>/gi, "") // Remove center tags
      .replace(/\[\[File:[^\]]+\]\]/g, "") // Remove file links
      .replace(/&nbsp;/g, " ")
      .replace(/&lt;/g, "<")
      .replace(/&gt;/g, ">")
      .replace(/&amp;/g, "&")
      .replace(/\s+/g, " ") // Normalize whitespace
      .trim()
  }

  private extractInfoboxDataRobust(wikitext: string): Record<string, string> {
    const infoboxData: Record<string, string> = {}

    // Try common infobox templates first
    const commonTemplates = [
      "Blox Fruit Infobox",
      "Infobox",
      "Fruit",
      "Sword",
      "Gun",
      "Accessory",
      "NPC",
      "Quest",
      "Raid",
      "Material",
      "Enemy Infobox",
      "Weapon Infobox", // Added Weapon Infobox
    ]
    for (const template of commonTemplates) {
      const data = this.extractTemplateData(wikitext, template)
      if (Object.keys(data).length > 0) {
        Object.assign(infoboxData, data)
        break // Found a main infobox, stop
      }
    }

    // Fallback: try to extract from any template that looks like an infobox
    if (Object.keys(infoboxData).length === 0) {
      const genericInfoboxMatch = wikitext.match(/\{\{([^|\n]+)\|([\s\S]*?)\}\}/i)
      if (genericInfoboxMatch) {
        const templateName = genericInfoboxMatch[1].trim()
        const data = this.extractTemplateData(wikitext, templateName)
        Object.assign(infoboxData, data)
      }
    }

    return infoboxData
  }

  private extractMoves(wikitext: string): Array<any> {
    const moves: Array<any> = []

    // Chercher les sections de moves
    const moveSectionPatterns = [
      /==\s*Moves?\s*==([\s\S]*?)(?===|\{\{|$)/i,
      /==\s*Abilities\s*==([\s\S]*?)(?===|$)/i,
      /==\s*Skills?\s*==([\s\S]*?)(?===|$)/i,
      /==\s*Attacks?\s*==([\s\S]*?)(?===|$)/i,
      /==\s*Moveset\s*==([\s\S]*?)(?===|$)/i,
    ]

    let moveSection = ""
    for (const pattern of moveSectionPatterns) {
      const match = wikitext.match(pattern)
      if (match) {
        moveSection = match[1]
        break
      }
    }

    if (!moveSection) {
      return moves
    }

    // Extract individual moves using SkillBox template
    const skillBoxPattern = /\{\{SkillBox\s*\|([\s\S]*?)\}\}/g
    let skillBoxMatch

    while ((skillBoxMatch = skillBoxPattern.exec(moveSection)) !== null) {
      const skillBoxContent = skillBoxMatch[1]
      const skillBoxData = this.extractTemplateData(`{{SkillBox|${skillBoxContent}}}`, "SkillBox")

      const move: any = {
        name: skillBoxData.move || skillBoxData.name,
        key:
          skillBoxData.tap ||
          skillBoxData.z ||
          skillBoxData.x ||
          skillBoxData.c ||
          skillBoxData.v ||
          skillBoxData.f ||
          skillBoxData.key,
        description: skillBoxData.desc || skillBoxData.description,
        damage: this.parseNumber(skillBoxData.damage),
        cooldown: this.parseNumber(skillBoxData.cooldown),
        mastery: this.parseNumber(skillBoxData.mas || skillBoxData.mastery),
        energy: this.parseNumber(skillBoxData.energy),
        type: skillBoxData.type,
      }

      // Clean up move keys
      if (move.key) {
        move.key = move.key.toUpperCase().replace(/\$/g, "").trim()
        if (move.key.length > 1 && !["TAP", "Z", "X", "C", "V", "F"].includes(move.key)) {
          move.key = undefined // Remove invalid keys
        }
      }

      if (move.name && move.name.length > 1 && move.name.toLowerCase() !== "normal attack") {
        moves.push(move)
      }
    }

    return moves
  }

  private extractKeyFromText(text: string): string | undefined {
    const keyPatterns = [
      /\$([A-Z])\$/, // $Z$
      /key:?\s*([A-Z])/i, // key: Z
      /press\s*([A-Z])/i, // press Z
      /\[([A-Z])\]/, // [Z]
      /^([A-Z])$/m, // Z (single char on a line)
    ]

    for (const pattern of keyPatterns) {
      const match = text.match(pattern)
      if (match && match[1] && match[1].length === 1) {
        return match[1].toUpperCase()
      }
    }
    return undefined
  }

  private extractMoveStats(content: string): Record<string, any> {
    const stats: Record<string, any> = {}

    const statPatterns = [
      { key: "damage", patterns: [/(\d+(?:\.\d+)?)\s*damage/i, /dmg:?\s*(\d+(?:\.\d+)?)/i] },
      { key: "cooldown", patterns: [/(\d+(?:\.\d+)?)\s*(?:second|sec|s)\s*cooldown/i, /cd:?\s*(\d+(?:\.\d+)?)/i] },
      { key: "mastery", patterns: [/(\d+)\s*mastery/i, /req:?\s*(\d+)/i] },
      { key: "energy", patterns: [/(\d+)\s*energy/i, /stamina:?\s*(\d+)/i] },
      { key: "type", patterns: [/type:?\s*([a-zA-Z]+)/i] },
    ]

    statPatterns.forEach(({ key, patterns }) => {
      for (const pattern of patterns) {
        const match = content.match(pattern)
        if (match) {
          stats[key] = key === "type" ? this.cleanWikitext(match[1]) : this.parseNumber(match[1])
          break
        }
      }
    })

    return stats
  }

  private extractMoveDescription(content: string): string | null {
    const cleaned = this.cleanWikitext(content)
    const sentences = cleaned.split(/[.!?]/)

    for (const sentence of sentences) {
      const trimmed = sentence.trim()
      if (trimmed.length > 10 && !trimmed.match(/^\d+$/) && !trimmed.match(/^[A-Z]$/)) {
        return trimmed
      }
    }
    return cleaned.length > 10 ? cleaned.substring(0, 100) + "..." : null
  }

  private extractDescription(wikitext: string): string | null {
    // Try to find the main description, usually the first paragraph after infoboxes/headers
    const lines = wikitext.split("\n")
    let description = ""
    let inInfobox = false
    let foundMeaningfulLine = false

    for (const line of lines) {
      const trimmed = line.trim()

      if (
        trimmed.startsWith("{{Infobox") ||
        trimmed.startsWith("{{Blox Fruit Infobox") ||
        trimmed.startsWith("{{Weapon Infobox")
      ) {
        inInfobox = true
        continue
      }
      if (inInfobox && trimmed.endsWith("}}")) {
        inInfobox = false
        continue
      }
      if (inInfobox) continue

      // Skip headers, categories, empty lines, and short lines
      if (
        trimmed.startsWith("==") ||
        trimmed.startsWith("[[Category:") ||
        trimmed.length < 10 ||
        trimmed.match(/^\s*\|\s*\w+\s*=\s*[^|]*$/) // Skip infobox parameters outside infobox block
      ) {
        continue
      }

      // If we find a line that is not a template or header, consider it part of description
      if (trimmed.length > 0) {
        description += trimmed + " "
        foundMeaningfulLine = true
        // Stop after a certain length or number of lines to avoid scraping entire wiki page
        if (description.length > 300) break
      }
    }

    if (foundMeaningfulLine) {
      const cleanedDesc = this.cleanWikitext(description)
      return cleanedDesc.length > 20 ? cleanedDesc.substring(0, 300) + (cleanedDesc.length > 300 ? "..." : "") : null
    }

    return null
  }

  private parseNumber(value: string): number | undefined {
    if (!value) return undefined
    const cleaned = value.replace(/[^\d.]/g, "")
    const num = Number.parseFloat(cleaned)
    return isNaN(num) ? undefined : num
  }

  async scrapeItem(title: string, category: string): Promise<ScrapedItem | null> {
    console.log(`📄 Scraping: ${title} (Category: ${category})`)

    const wikitext = await this.getPageContent(title)
    if (!wikitext) {
      console.warn(`⚠️ No content found for: ${title}`)
      return null
    }

    const infoboxData = this.extractInfoboxDataRobust(wikitext)
    const moves = this.extractMoves(wikitext)
    const description = this.extractDescription(wikitext)
    const stats = this.extractStats(wikitext, infoboxData)

    // Extraire les données spécialisées selon le type d'item
    let materialData, fruitData, weaponData, npcData, questData, enemyData

    if (category === "material") {
      materialData = this.extractMaterialData(wikitext)

      // Ajouter des informations spécifiques aux matériaux depuis l'infobox
      if (infoboxData.max) {
        materialData.maxStack = this.parseNumber(infoboxData.max)
      }
      if (infoboxData.source) {
        materialData.source = infoboxData.source
      }

      // Extraire les informations de spawn depuis les notes
      const notesMatch = wikitext.match(/==\s*Notes\s*==([\s\S]*?)(?===|$)/i)
      if (notesMatch) {
        const spawnRateMatch = notesMatch[1].match(/spawn every (\d+) minutes/i)
        if (spawnRateMatch) {
          materialData.spawnRate = `${spawnRateMatch[1]} minutes`
        }

        const despawnMatch = notesMatch[1].match(/take (\d+) hour[s]? to despawn/i)
        if (despawnMatch) {
          materialData.despawnTime = `${despawnMatch[1]} hour${despawnMatch[1] !== '1' ? 's' : ''}`
        }
      }
    }

    if (category === "fruit") {
      fruitData = this.extractFruitData(wikitext, infoboxData)
    }

    if (category === "sword" || category === "gun") {
      weaponData = this.extractWeaponData(wikitext, infoboxData)
    }

    if (category === "npc") {
      npcData = this.extractNPCData(wikitext, infoboxData)
    }

    if (category === "quest") {
      questData = this.extractQuestData(wikitext, infoboxData)
    }

    if (category === "raid" || category === "enemy" || infoboxData.type === "Enemy") {
      enemyData = this.extractEnemyData(wikitext, infoboxData)
    }

    // Determine type based on category and data
    let type = infoboxData.type || "Unknown"
    if (category === "fruit") {
      const fruitTypes = ["Paramecia", "Logia", "Zoan", "Beast"] // Added Beast
      const foundType = fruitTypes.find((t) => wikitext.toLowerCase().includes(t.toLowerCase()))
      if (foundType) type = foundType
      else if (infoboxData.fruit_type) type = infoboxData.fruit_type
    } else if (category === "sword") {
      if (infoboxData.sword_type) type = infoboxData.sword_type
    } else if (category === "gun") {
      if (infoboxData.gun_type) type = infoboxData.gun_type
    } else if (category === "accessory") {
      if (infoboxData.accessory_type) type = infoboxData.accessory_type
    } else if (category === "npc") {
      if (infoboxData.npc_type) type = infoboxData.npc_type
    }

    const item: ScrapedItem = {
      name: title,
      type,
      category,
      rarity: infoboxData.rarity,
      price: infoboxData.money || infoboxData.price || infoboxData.cost || infoboxData.beli, // Prioritize money
      robuxPrice: infoboxData.robux || infoboxData.robux_price,
      description: description || undefined,
      stats: stats.length > 0 ? stats : undefined,
      moves: moves.length > 0 ? moves : undefined,
      imageUrl: infoboxData.image
        ? `https://static.wikia.nocookie.net/blox-fruits/images/${infoboxData.image.replace(/ /g, "_")}` // Replace spaces for image URLs
        : undefined,
      wikiUrl: `https://blox-fruits.fandom.com/wiki/${title.replace(/ /g, "_")}`,
      lastUpdated: new Date(),
      obtainment: infoboxData.obtainment || infoboxData.source,
      upgrading: infoboxData.upgrading,
      location: infoboxData.location || infoboxData.island || infoboxData.spawn_location,
      requirements: infoboxData.requirements ? [infoboxData.requirements] : undefined,
      rewards: infoboxData.rewards ? [infoboxData.rewards] : undefined,
      hp: this.parseNumber(infoboxData.hp || infoboxData.health),
      level: this.parseNumber(infoboxData.level || infoboxData.npc_level),
      // Ajouter les données spécialisées
      materialData: materialData && Object.keys(materialData).length > 0 ? materialData : undefined,
      fruitData: fruitData && Object.keys(fruitData).length > 0 ? fruitData : undefined,
      weaponData: weaponData && Object.keys(weaponData).length > 0 ? weaponData : undefined,
      npcData: npcData && Object.keys(npcData).length > 0 ? npcData : undefined,
      questData: questData && Object.keys(questData).length > 0 ? questData : undefined,
      enemyData: enemyData && Object.keys(enemyData).length > 0 ? enemyData : undefined,
      rawData: {
        infobox: infoboxData,
        movesRaw: moves.length > 0 ? JSON.stringify(moves) : undefined, // Store raw moves for analysis
        descriptionRaw: description || undefined,
        fullWikitextSample: wikitext.substring(0, Math.min(wikitext.length, 2000)), // Sample of full wikitext
        wikitextLength: wikitext.length,
        movesFound: moves.length,
        statsFound: stats.length,
        extractedAt: new Date().toISOString(),
        materialData: materialData || undefined,
        fruitData: fruitData || undefined,
        weaponData: weaponData || undefined,
        npcData: npcData || undefined,
        questData: questData || undefined,
        enemyData: enemyData || undefined,
      },
    }

    // Clean up undefined values
    Object.keys(item).forEach((key) => {
      if (item[key as keyof ScrapedItem] === undefined) {
        delete item[key as keyof ScrapedItem]
      }
    })

    console.log(`✅ Scraped ${title}: ${Object.keys(item).length} fields, ${moves.length} moves`)
    return item
  }

  private extractStats(wikitext: string, infoboxData: Record<string, string>): string[] {
    const stats: string[] = []

    // Stats from infobox
    if (infoboxData.awakening === "Yes" || infoboxData.awakening === "Available") {
      stats.push("Awakening: Available")
    }
    if (infoboxData.flight === "Yes") stats.push("Flight Ability")
    if (infoboxData.transform === "Yes") stats.push("Transformation")
    if (infoboxData.instinct === "Yes" || wikitext.toLowerCase().includes("instinct-breaking"))
      stats.push("Instinct Break")
    if (infoboxData.elemental_immunity === "Yes") stats.push("Elemental Immunity")
    if (infoboxData.logia_immunity === "Yes") stats.push("Logia Immunity")
    if (infoboxData.stat_boost) stats.push(`Stat Boost: ${infoboxData.stat_boost}`)
    if (infoboxData.effect) stats.push(`Effect: ${infoboxData.effect}`)
    if (infoboxData.cooldown_reduction) stats.push(`Cooldown Reduction: ${infoboxData.cooldown_reduction}`)
    if (infoboxData.damage_resistance) stats.push(`Damage Resistance: ${infoboxData.damage_resistance}`)

    // Stats from text (more generic)
    const textStatPatterns = [
      /flight\s*ability/i,
      /transformation/i,
      /awakening/i,
      /instinct\s*break/i,
      /elemental\s*immunity/i,
      /logia\s*immunity/i,
      /damage\s*reduction/i,
      /speed\s*boost/i,
      /health\s*regeneration/i,
      /energy\s*regeneration/i,
      /high\s*damage/i,
      /large\s*aoe/i,
      /good\s*mobility/i,
      /stun/i,
      /burn\s*damage/i,
      /damage\s*resistance/i,
    ]

    textStatPatterns.forEach((pattern) => {
      if (pattern.test(wikitext)) {
        const statName = pattern.source.replace(/\\s\*/g, " ").replace(/[\\]/g, "")
        const cleanedStatName = statName.charAt(0).toUpperCase() + statName.slice(1)
        if (!stats.some((s) => s.toLowerCase().includes(cleanedStatName.toLowerCase()))) {
          stats.push(cleanedStatName)
        }
      }
    })

    return stats
  }

  async scrapeCategory(categoryName: string, itemType: string): Promise<ScrapedItem[]> {
    console.log(`\n🎯 Starting to scrape category: ${categoryName}`)

    const members = await this.getCategoryMembers(categoryName)
    if (members.length === 0) {
      return []
    }

    const items: ScrapedItem[] = []
    let processed = 0

    for (const member of members) {
      try {
        const item = await this.scrapeItem(member.title, itemType)
        if (item) {
          items.push(item)
        }
        processed++

        // Progress indicator
        if (processed % 5 === 0 || processed === members.length) {
          console.log(
            `📊 Progress: ${processed}/${members.length} (${Math.round((processed / members.length) * 100)}%)`,
          )
        }

        // Small delay between items
        await new Promise((resolve) => setTimeout(resolve, 200))
      } catch (error) {
        console.error(`❌ Error scraping ${member.title}:`, error)
      }
    }

    console.log(`✅ Completed ${categoryName}: ${items.length}/${members.length} items scraped`)
    return items
  }

  async scrapeAll(): Promise<void> {
    console.log("🚀 Starting comprehensive scraping of all categories...")

    await this.mongoClient.connect()
    const db = this.mongoClient.db("bloxfruits")

    const categories = [
      { name: "Blox_Fruits", type: "fruit", collection: "fruits" },
      { name: "Swords", type: "sword", collection: "swords" },
      { name: "Accessories", type: "accessory", collection: "accessories" },
      { name: "Guns", type: "gun", collection: "guns" },
      { name: "Materials", type: "material", collection: "materials" },
      { name: "NPCs", type: "npc", collection: "npcs" },
      { name: "Quests", type: "quest", collection: "quests" },
      { name: "Raids", type: "raid", collection: "raids" },
      { name: "Game_Mechanics", type: "mechanic", collection: "mechanics" }, // Added mechanics category
    ]

    for (const category of categories) {
      try {
        const items = await this.scrapeCategory(category.name, category.type)

        if (items.length > 0) {
          const collection = db.collection(category.collection)
          await collection.deleteMany({})
          await collection.insertMany(items)
          console.log(`💾 Saved ${items.length} ${category.type}s to database`)
        } else {
          console.warn(`⚠️ No items to save for ${category.name}`)
        }

        // Pause between categories
        console.log("⏸️ Pausing 5 seconds before next category...")
        await new Promise((resolve) => setTimeout(resolve, 5000))
      } catch (error) {
        console.error(`❌ Error processing category ${category.name}:`, error)
      }
    }

    await this.mongoClient.close()
    console.log("🎉 All categories scraped successfully!")
  }
}
