import dotenv from "dotenv"
import { FandomAPIScraper } from "../lib/scrapers/fandom-api-scraper"

// Charger les variables d'environnement
dotenv.config({ path: ".env.local" })

async function testSingleItem() {
  const mongoUrl = process.env.MONGODB_URL
  if (!mongoUrl) {
    console.error("❌ MONGODB_URL environment variable is required")
    console.log("💡 Make sure you have a .env.local file with MONGODB_URL=your_mongodb_connection_string")
    process.exit(1)
  }

  const scraper = new FandomAPIScraper(mongoUrl)

  // --- Configure the item you want to test ---
  const itemTitle = "Cursed Dual Katana" // Example: "Dragon", "Yama", "Blue Spikey Coat", "Rubber", "Acidum Rifle", "Area 2 Quest Giver", "Hungry Man/Quest", "Bone Breaker"
  const itemCategory = "sword" // Example: "fruit", "sword", "accessory", "gun", "npc", "quest", "raid", "mechanic"
  // ------------------------------------------

  console.log(`\n🧪 Testing single item scraping for: "${itemTitle}" (${itemCategory})`)
  console.log("==================================================================")

  try {
    const item = await scraper.scrapeItem(itemTitle, itemCategory)

    if (item) {
      console.log("\n✅ Successfully scraped item:")
      console.dir(item, { depth: null, colors: true }) // Log the full object
      console.log("\n--- Raw Wikitext Sample (first 2000 chars) ---")
      console.log(item.rawData.fullWikitextSample)
      console.log("----------------------------------------------")
    } else {
      console.warn(`⚠️ Could not scrape item: ${itemTitle}`)
    }
  } catch (error) {
    console.error(`❌ Error during single item test for ${itemTitle}:`, error)
  } finally {
    // Ensure MongoDB client is closed if it was opened internally by scrapeItem
    // (though in this setup, it's only opened in scrapeAll)
  }
}

testSingleItem()
