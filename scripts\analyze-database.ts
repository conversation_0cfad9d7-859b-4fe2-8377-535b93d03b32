import dotenv from "dotenv"
import { MongoClient } from "mongodb"

// Charger les variables d'environnement
dotenv.config({ path: ".env.local" })

interface DatabaseItem {
  _id: any
  name: string
  type: string
  category: string
  rarity?: string
  price?: string
  robuxPrice?: string
  level?: number
  moves?: Array<any>
  stats?: Array<any>
  weaponData?: any
  fruitData?: any
  npcData?: any
  enemyData?: any
  materialData?: any
  accessoryData?: any
  mechanicData?: any
  questData?: any
  rawData?: any
  imageUrls?: Array<string>
  lastUpdated?: Date
}

async function analyzeDatabaseInconsistencies() {
  const mongoUrl = process.env.MONGODB_URL
  if (!mongoUrl) {
    console.error("❌ MONGODB_URL environment variable is required")
    process.exit(1)
  }

  console.log("🔍 ANALYSE DES INCOHÉRENCES DE LA BASE DE DONNÉES")
  console.log("================================================")
  console.log("Lecture directe de la base de données pour identifier les problèmes")
  console.log("")

  const client = new MongoClient(mongoUrl)

  try {
    await client.connect()
    const db = client.db("bloxfruits")
    
    // Obtenir toutes les collections
    const collections = await db.listCollections().toArray()
    console.log(`📊 Collections trouvées: ${collections.map(c => c.name).join(', ')}`)
    
    // Analyser la collection principale (items ou fruits)
    const itemsCollection = db.collection("items") // Essayer items d'abord
    let itemsCount = await itemsCollection.countDocuments()
    
    if (itemsCount === 0) {
      // Essayer d'autres collections
      const fruitsCollection = db.collection("fruits")
      const fruitsCount = await fruitsCollection.countDocuments()
      
      if (fruitsCount > 0) {
        console.log(`📊 Utilisation de la collection 'fruits': ${fruitsCount} items`)
        await analyzeCollection(fruitsCollection, "fruits")
      } else {
        console.log("❌ Aucune donnée trouvée dans les collections items ou fruits")
        
        // Lister toutes les collections avec des données
        for (const collection of collections) {
          const count = await db.collection(collection.name).countDocuments()
          if (count > 0) {
            console.log(`📊 Collection '${collection.name}': ${count} documents`)
            
            // Analyser les premières entrées
            const samples = await db.collection(collection.name).find({}).limit(3).toArray()
            samples.forEach((sample, index) => {
              console.log(`   Sample ${index + 1}:`, {
                name: sample.name || 'N/A',
                type: sample.type || 'N/A',
                category: sample.category || 'N/A'
              })
            })
          }
        }
      }
    } else {
      console.log(`📊 Utilisation de la collection 'items': ${itemsCount} items`)
      await analyzeCollection(itemsCollection, "items")
    }

  } catch (error) {
    console.error("❌ Erreur lors de l'analyse:", error)
  } finally {
    await client.close()
  }
}

async function analyzeCollection(collection: any, collectionName: string) {
  console.log(`\n🔍 ANALYSE DE LA COLLECTION '${collectionName.toUpperCase()}'`)
  console.log("=" + "=".repeat(collectionName.length + 25))
  
  // 1. Analyse des catégories
  console.log("\n📂 ANALYSE DES CATÉGORIES:")
  const categoryStats = await collection.aggregate([
    { $group: { _id: "$category", count: { $sum: 1 } } },
    { $sort: { count: -1 } }
  ]).toArray()
  
  categoryStats.forEach(stat => {
    console.log(`   ${stat._id || 'undefined'}: ${stat.count} items`)
  })
  
  // 2. Analyse des types vs catégories
  console.log("\n🏷️ ANALYSE TYPE VS CATÉGORIE:")
  const typeVsCategory = await collection.aggregate([
    { $group: { _id: { category: "$category", type: "$type" }, count: { $sum: 1 } } },
    { $sort: { "_id.category": 1, count: -1 } }
  ]).toArray()
  
  const inconsistencies: string[] = []
  typeVsCategory.forEach(stat => {
    const category = stat._id.category
    const type = stat._id.type
    const count = stat.count
    
    console.log(`   ${category} → ${type}: ${count} items`)
    
    // Détecter les incohérences
    if (category === "sword" && type !== "Sword") {
      inconsistencies.push(`❌ Catégorie 'sword' avec type '${type}' (${count} items)`)
    }
    if (category === "fruit" && !["Paramecia", "Logia", "Zoan", "Beast", "Natural"].includes(type)) {
      inconsistencies.push(`❌ Catégorie 'fruit' avec type '${type}' (${count} items)`)
    }
    if (category === "enemy" && type !== "Enemy") {
      inconsistencies.push(`❌ Catégorie 'enemy' avec type '${type}' (${count} items)`)
    }
  })
  
  // 3. Analyse des données manquantes
  console.log("\n📊 ANALYSE DES DONNÉES MANQUANTES:")
  
  const missingData = await collection.aggregate([
    {
      $project: {
        name: 1,
        category: 1,
        hasPrice: { $cond: [{ $ne: ["$price", null] }, 1, 0] },
        hasRobuxPrice: { $cond: [{ $ne: ["$robuxPrice", null] }, 1, 0] },
        hasImages: { $cond: [{ $gt: [{ $size: { $ifNull: ["$imageUrls", []] } }, 0] }, 1, 0] },
        hasMoves: { $cond: [{ $gt: [{ $size: { $ifNull: ["$moves", []] } }, 0] }, 1, 0] },
        hasWeaponData: { $cond: [{ $ne: ["$weaponData", null] }, 1, 0] },
        hasFruitData: { $cond: [{ $ne: ["$fruitData", null] }, 1, 0] },
        hasNpcData: { $cond: [{ $ne: ["$npcData", null] }, 1, 0] },
        hasEnemyData: { $cond: [{ $ne: ["$enemyData", null] }, 1, 0] }
      }
    },
    {
      $group: {
        _id: "$category",
        count: { $sum: 1 },
        withPrice: { $sum: "$hasPrice" },
        withRobuxPrice: { $sum: "$hasRobuxPrice" },
        withImages: { $sum: "$hasImages" },
        withMoves: { $sum: "$hasMoves" },
        withWeaponData: { $sum: "$hasWeaponData" },
        withFruitData: { $sum: "$hasFruitData" },
        withNpcData: { $sum: "$hasNpcData" },
        withEnemyData: { $sum: "$hasEnemyData" }
      }
    }
  ]).toArray()
  
  missingData.forEach(stat => {
    const category = stat._id
    const total = stat.count
    
    console.log(`\n   📂 ${category} (${total} items):`)
    console.log(`     💰 Prix: ${stat.withPrice}/${total} (${((stat.withPrice/total)*100).toFixed(1)}%)`)
    console.log(`     💎 Robux: ${stat.withRobuxPrice}/${total} (${((stat.withRobuxPrice/total)*100).toFixed(1)}%)`)
    console.log(`     🖼️ Images: ${stat.withImages}/${total} (${((stat.withImages/total)*100).toFixed(1)}%)`)
    console.log(`     ⚔️ Moves: ${stat.withMoves}/${total} (${((stat.withMoves/total)*100).toFixed(1)}%)`)
    
    if (category === "sword" || category === "gun") {
      console.log(`     🔫 WeaponData: ${stat.withWeaponData}/${total} (${((stat.withWeaponData/total)*100).toFixed(1)}%)`)
    }
    if (category === "fruit") {
      console.log(`     🍎 FruitData: ${stat.withFruitData}/${total} (${((stat.withFruitData/total)*100).toFixed(1)}%)`)
    }
    if (category === "npc") {
      console.log(`     👤 NpcData: ${stat.withNpcData}/${total} (${((stat.withNpcData/total)*100).toFixed(1)}%)`)
    }
    if (category === "enemy") {
      console.log(`     👹 EnemyData: ${stat.withEnemyData}/${total} (${((stat.withEnemyData/total)*100).toFixed(1)}%)`)
    }
  })
  
  // 4. Exemples d'items problématiques
  console.log("\n🚨 EXEMPLES D'ITEMS PROBLÉMATIQUES:")
  
  // Items avec catégorie/type incohérents
  const problematicItems = await collection.find({
    $or: [
      { category: "sword", type: { $ne: "Sword" } },
      { category: "fruit", type: { $nin: ["Paramecia", "Logia", "Zoan", "Beast", "Natural"] } },
      { category: "enemy", type: { $ne: "Enemy" } },
      { category: "npc", npcData: null },
      { category: "sword", weaponData: null },
      { category: "fruit", fruitData: null }
    ]
  }).limit(10).toArray()
  
  problematicItems.forEach((item: DatabaseItem) => {
    console.log(`   ❌ ${item.name}:`)
    console.log(`     - Catégorie: ${item.category}, Type: ${item.type}`)
    
    if (item.category === "npc" && !item.npcData) {
      console.log(`     - Problème: NPC sans npcData`)
    }
    if ((item.category === "sword" || item.category === "gun") && !item.weaponData) {
      console.log(`     - Problème: Arme sans weaponData`)
    }
    if (item.category === "fruit" && !item.fruitData) {
      console.log(`     - Problème: Fruit sans fruitData`)
    }
  })
  
  // 5. Résumé des incohérences
  console.log("\n📋 RÉSUMÉ DES INCOHÉRENCES:")
  if (inconsistencies.length > 0) {
    inconsistencies.forEach(inc => console.log(`   ${inc}`))
  } else {
    console.log("   ✅ Aucune incohérence majeure détectée dans les types/catégories")
  }
  
  // 6. Recommandations
  console.log("\n💡 RECOMMANDATIONS:")
  console.log("   🔧 Corriger les catégories/types incohérents")
  console.log("   📊 Améliorer l'extraction des données spécialisées")
  console.log("   🖼️ Augmenter le taux d'extraction des images")
  console.log("   ⚔️ Améliorer l'extraction des moves pour les armes")
}

// Exécuter l'analyse
analyzeDatabaseInconsistencies().catch(console.error)
