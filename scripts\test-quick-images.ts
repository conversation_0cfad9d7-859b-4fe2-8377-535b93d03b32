import dotenv from "dotenv"
import { FandomAPIScraper } from "../lib/scrapers/fandom-api-scraper"

// Charger les variables d'environnement
dotenv.config({ path: ".env.local" })

async function testQuickImages() {
  const mongoUrl = process.env.MONGODB_URL
  if (!mongoUrl) {
    console.error("❌ MONGODB_URL environment variable is required")
    console.log("💡 Create a .env.local file with:")
    console.log("MONGODB_URL=mongodb://localhost:27017/bloxfruits")
    process.exit(1)
  }

  console.log("⚡ QUICK IMAGE EXTRACTION TEST")
  console.log("==============================")
  console.log("Fast test of image extraction with popular items")
  console.log("")

  const scraper = new FandomAPIScraper(mongoUrl)

  try {
    // Test avec quelques items populaires
    const testItems = [
      { name: "Dragon", category: "fruit" },
      { name: "<PERSON><PERSON>", category: "accessory" },
      { name: "Bazooka", category: "gun" }
    ]
    
    console.log(`🔍 Testing ${testItems.length} popular items...`)
    
    for (const testItem of testItems) {
      try {
        console.log(`\n📄 Testing ${testItem.name}...`)
        const item = await scraper.scrapeItem(testItem.name, testItem.category)
        
        if (item && item.imageUrls && item.imageUrls.length > 0) {
          console.log(`✅ ${testItem.name}: ${item.imageUrls.length} images found!`)
          item.imageUrls.slice(0, 2).forEach((url, index) => {
            console.log(`   ${index + 1}. ${url}`)
          })
          if (item.imageUrls.length > 2) {
            console.log(`   ... and ${item.imageUrls.length - 2} more`)
          }
        } else {
          console.log(`❌ ${testItem.name}: No images found`)
        }
        
        await new Promise(resolve => setTimeout(resolve, 1000))
        
      } catch (error) {
        console.log(`❌ ${testItem.name}: Error - ${error}`)
      }
    }

  } catch (error) {
    console.error("❌ Error during quick image test:", error)
  }
  
  console.log("\n⚡ Quick test completed!")
}

// Exécuter le test
testQuickImages().catch(console.error)
