import dotenv from "dotenv"
import { FandomAPIScraper } from "../lib/scrapers/fandom-api-scraper"

// Charger les variables d'environnement
dotenv.config({ path: ".env.local" })

async function testAllEnhancedScraper() {
  const mongoUrl = process.env.MONGODB_URL
  if (!mongoUrl) {
    console.error("❌ MONGODB_URL environment variable is required")
    console.log("💡 Create a .env.local file with:")
    console.log("MONGODB_URL=mongodb://localhost:27017/bloxfruits")
    process.exit(1)
  }

  console.log("🚀 COMPREHENSIVE ENHANCED SCRAPER TEST")
  console.log("======================================")
  console.log("Testing all enhanced scraper functionalities")
  console.log("")

  const scraper = new FandomAPIScraper(mongoUrl)
  const results = {
    materials: { success: 0, failed: 0 },
    fruits: { success: 0, failed: 0 },
    npcs: { success: 0, failed: 0 },
    quests: { success: 0, failed: 0 },
    enemies: { success: 0, failed: 0 },
    weapons: { success: 0, failed: 0 }
  }

  try {
    // Test Materials
    console.log("🧪 TESTING MATERIALS")
    console.log("====================")
    const testMaterials = ["Berries", "Fragment", "Bones"]
    
    for (const material of testMaterials) {
      try {
        const item = await scraper.scrapeItem(material, "material")
        if (item && item.materialData) {
          console.log(`✅ ${material}: Material data extracted`)
          results.materials.success++
        } else {
          console.log(`⚠️ ${material}: No material data`)
          results.materials.failed++
        }
      } catch (error) {
        console.log(`❌ ${material}: Error`)
        results.materials.failed++
      }
      await new Promise(resolve => setTimeout(resolve, 500))
    }

    // Test Fruits
    console.log("\n🍎 TESTING FRUITS")
    console.log("=================")
    const testFruits = ["Dragon", "Leopard", "Buddha"]
    
    for (const fruit of testFruits) {
      try {
        const item = await scraper.scrapeItem(fruit, "fruit")
        if (item && item.fruitData) {
          console.log(`✅ ${fruit}: Fruit data extracted`)
          results.fruits.success++
        } else {
          console.log(`⚠️ ${fruit}: No fruit data`)
          results.fruits.failed++
        }
      } catch (error) {
        console.log(`❌ ${fruit}: Error`)
        results.fruits.failed++
      }
      await new Promise(resolve => setTimeout(resolve, 500))
    }

    // Test NPCs
    console.log("\n👥 TESTING NPCs")
    console.log("===============")
    const testNPCs = ["Arowe", "Alchemist"]
    
    for (const npc of testNPCs) {
      try {
        const item = await scraper.scrapeItem(npc, "npc")
        if (item && item.npcData) {
          console.log(`✅ ${npc}: NPC data extracted`)
          results.npcs.success++
        } else {
          console.log(`⚠️ ${npc}: No NPC data`)
          results.npcs.failed++
        }
      } catch (error) {
        console.log(`❌ ${npc}: Error`)
        results.npcs.failed++
      }
      await new Promise(resolve => setTimeout(resolve, 500))
    }

    // Test Quests
    console.log("\n🎯 TESTING QUESTS")
    console.log("=================")
    const testQuests = ["Alchemist"]
    
    for (const quest of testQuests) {
      try {
        const item = await scraper.scrapeItem(quest, "quest")
        if (item && item.questData) {
          console.log(`✅ ${quest}: Quest data extracted`)
          results.quests.success++
        } else {
          console.log(`⚠️ ${quest}: No quest data`)
          results.quests.failed++
        }
      } catch (error) {
        console.log(`❌ ${quest}: Error`)
        results.quests.failed++
      }
      await new Promise(resolve => setTimeout(resolve, 500))
    }

    // Test Enemies
    console.log("\n⚔️ TESTING ENEMIES")
    console.log("==================")
    const testEnemies = ["Shocker"]
    
    for (const enemy of testEnemies) {
      try {
        const item = await scraper.scrapeItem(enemy, "enemy")
        if (item && item.enemyData) {
          console.log(`✅ ${enemy}: Enemy data extracted`)
          results.enemies.success++
        } else {
          console.log(`⚠️ ${enemy}: No enemy data`)
          results.enemies.failed++
        }
      } catch (error) {
        console.log(`❌ ${enemy}: Error`)
        results.enemies.failed++
      }
      await new Promise(resolve => setTimeout(resolve, 500))
    }

    // Test Weapons
    console.log("\n⚔️ TESTING WEAPONS")
    console.log("==================")
    const testWeapons = ["Katana", "Cutlass"]
    
    for (const weapon of testWeapons) {
      try {
        const item = await scraper.scrapeItem(weapon, "sword")
        if (item && item.weaponData) {
          console.log(`✅ ${weapon}: Weapon data extracted`)
          results.weapons.success++
        } else {
          console.log(`⚠️ ${weapon}: No weapon data`)
          results.weapons.failed++
        }
      } catch (error) {
        console.log(`❌ ${weapon}: Error`)
        results.weapons.failed++
      }
      await new Promise(resolve => setTimeout(resolve, 500))
    }

    // Afficher les résultats finaux
    console.log("\n📊 FINAL RESULTS")
    console.log("================")
    console.log(`🧪 Materials: ${results.materials.success}/${results.materials.success + results.materials.failed} successful`)
    console.log(`🍎 Fruits: ${results.fruits.success}/${results.fruits.success + results.fruits.failed} successful`)
    console.log(`👥 NPCs: ${results.npcs.success}/${results.npcs.success + results.npcs.failed} successful`)
    console.log(`🎯 Quests: ${results.quests.success}/${results.quests.success + results.quests.failed} successful`)
    console.log(`⚔️ Enemies: ${results.enemies.success}/${results.enemies.success + results.enemies.failed} successful`)
    console.log(`🗡️ Weapons: ${results.weapons.success}/${results.weapons.success + results.weapons.failed} successful`)

    const totalSuccess = Object.values(results).reduce((sum, cat) => sum + cat.success, 0)
    const totalTests = Object.values(results).reduce((sum, cat) => sum + cat.success + cat.failed, 0)
    
    console.log(`\n🎯 OVERALL SUCCESS RATE: ${totalSuccess}/${totalTests} (${Math.round((totalSuccess/totalTests)*100)}%)`)

    console.log("\n✨ ENHANCED FEATURES TESTED:")
    console.log("============================")
    console.log("✅ Material data extraction (berry types, locations, usage)")
    console.log("✅ Fruit data extraction (type, awakening, values)")
    console.log("✅ NPC data extraction (type, location, services, dialogue)")
    console.log("✅ Quest data extraction (requirements, steps, difficulty)")
    console.log("✅ Enemy data extraction (stats, attacks, behavior)")
    console.log("✅ Weapon data extraction (damage, mastery, upgrades)")
    console.log("✅ Structured data storage in MongoDB")
    console.log("✅ Enhanced error handling and validation")

    if (totalSuccess === totalTests) {
      console.log("\n🎉 ALL TESTS PASSED! The enhanced scraper is working perfectly!")
    } else if (totalSuccess > totalTests * 0.8) {
      console.log("\n✅ Most tests passed! The enhanced scraper is working well!")
    } else {
      console.log("\n⚠️ Some tests failed. Check the logs above for details.")
    }

  } catch (error) {
    console.error("❌ Error during comprehensive testing:", error)
  }
  
  console.log("\n🏁 Comprehensive enhanced scraper testing completed!")
}

// Exécuter le test
testAllEnhancedScraper().catch(console.error)
