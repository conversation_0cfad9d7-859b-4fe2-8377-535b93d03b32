import dotenv from "dotenv"
import { FandomAPIScraper } from "../lib/scrapers/fandom-api-scraper"

// Charger les variables d'environnement
dotenv.config({ path: ".env.local" })

async function testMaterialScraper() {
  const mongoUrl = process.env.MONGODB_URL
  if (!mongoUrl) {
    console.error("❌ MONGODB_URL environment variable is required")
    console.log("💡 Create a .env.local file with:")
    console.log("MONGODB_URL=mongodb://localhost:27017/bloxfruits")
    process.exit(1)
  }

  console.log("🧪 TESTING MATERIAL SCRAPER")
  console.log("===========================")
  console.log("Testing enhanced scraper with Berries material data")
  console.log("")

  const scraper = new FandomAPIScraper(mongoUrl)

  try {
    // Test avec les données des Berries
    console.log("🔍 Testing Berries material scraping...")
    const berriesItem = await scraper.scrapeItem("Berries", "material")
    
    if (berriesItem) {
      console.log("✅ Successfully scraped Berries!")
      console.log("\n📊 EXTRACTED DATA:")
      console.log("==================")
      
      console.log(`📝 Name: ${berriesItem.name}`)
      console.log(`🏷️ Category: ${berriesItem.category}`)
      console.log(`⭐ Rarity: ${berriesItem.rarity}`)
      console.log(`📖 Description: ${berriesItem.description?.substring(0, 100)}...`)
      
      if (berriesItem.materialData) {
        console.log("\n🧪 MATERIAL DATA:")
        console.log("=================")
        
        if (berriesItem.materialData.berryTypes) {
          console.log(`🍓 Berry Types (${berriesItem.materialData.berryTypes.length}):`)
          berriesItem.materialData.berryTypes.forEach((berry, index) => {
            console.log(`   ${index + 1}. ${berry}`)
          })
        }
        
        if (berriesItem.materialData.locations) {
          console.log(`\n🗺️ Locations (${berriesItem.materialData.locations.length}):`)
          const locationsBySea = berriesItem.materialData.locations.reduce((acc, loc) => {
            if (!acc[loc.sea]) acc[loc.sea] = []
            acc[loc.sea].push(loc)
            return acc
          }, {} as Record<string, any[]>)
          
          Object.entries(locationsBySea).forEach(([sea, locations]) => {
            console.log(`   ${sea}:`)
            locations.forEach(loc => {
              console.log(`     - ${loc.location}: ${loc.bushes} bushes`)
            })
          })
        }
        
        if (berriesItem.materialData.usage) {
          console.log(`\n🎨 Usage (${berriesItem.materialData.usage.length} categories):`)
          berriesItem.materialData.usage.forEach(usage => {
            console.log(`   ${usage.type}: ${usage.items.length} items`)
            usage.items.slice(0, 3).forEach(item => {
              console.log(`     - ${item.name} (${item.rarity})`)
            })
            if (usage.items.length > 3) {
              console.log(`     ... and ${usage.items.length - 3} more`)
            }
          })
        }
        
        if (berriesItem.materialData.totalRequired) {
          console.log(`\n📊 Total Required for All Items:`)
          Object.entries(berriesItem.materialData.totalRequired).forEach(([berry, count]) => {
            console.log(`   ${berry}: ${count}`)
          })
        }
        
        if (berriesItem.materialData.maxStack) {
          console.log(`\n📦 Max Stack: ${berriesItem.materialData.maxStack}`)
        }
        
        if (berriesItem.materialData.source) {
          console.log(`🌿 Source: ${berriesItem.materialData.source}`)
        }
        
        if (berriesItem.materialData.spawnRate) {
          console.log(`⏰ Spawn Rate: ${berriesItem.materialData.spawnRate}`)
        }
        
        if (berriesItem.materialData.despawnTime) {
          console.log(`⏳ Despawn Time: ${berriesItem.materialData.despawnTime}`)
        }
      }
      
      console.log("\n🔍 RAW DATA ANALYSIS:")
      console.log("=====================")
      console.log(`📄 Wikitext Length: ${berriesItem.rawData.wikitextLength} characters`)
      console.log(`🎯 Moves Found: ${berriesItem.rawData.movesFound}`)
      console.log(`📈 Stats Found: ${berriesItem.rawData.statsFound}`)
      console.log(`🕒 Extracted At: ${berriesItem.rawData.extractedAt}`)
      
      if (berriesItem.rawData.materialData) {
        console.log("✅ Material-specific data successfully extracted")
      }
      
      console.log("\n📋 SAMPLE WIKITEXT:")
      console.log("===================")
      console.log(berriesItem.rawData.fullWikitextSample?.substring(0, 500) + "...")
      
    } else {
      console.log("❌ Failed to scrape Berries")
    }
    
    // Test avec d'autres matériaux si disponibles
    console.log("\n🔍 Testing other materials...")
    const otherMaterials = ["Fragment", "Bones", "Ectoplasm"]
    
    for (const material of otherMaterials) {
      try {
        console.log(`\n📄 Testing ${material}...`)
        const item = await scraper.scrapeItem(material, "material")
        if (item) {
          console.log(`✅ ${material}: Success`)
          if (item.materialData) {
            const dataKeys = Object.keys(item.materialData).filter(key => item.materialData![key as keyof typeof item.materialData])
            console.log(`   📊 Material data fields: ${dataKeys.join(", ")}`)
          }
        } else {
          console.log(`⚠️ ${material}: No data found`)
        }
      } catch (error) {
        console.log(`❌ ${material}: Error - ${error}`)
      }
    }
    
  } catch (error) {
    console.error("❌ Error during testing:", error)
  }
  
  console.log("\n🎉 Testing completed!")
}

// Exécuter le test
testMaterialScraper().catch(console.error)
