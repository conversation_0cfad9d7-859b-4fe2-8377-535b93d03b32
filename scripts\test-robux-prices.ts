import dotenv from "dotenv"
import { FandomAPIScraper } from "../lib/scrapers/fandom-api-scraper"

// Charger les variables d'environnement
dotenv.config({ path: ".env.local" })

async function testRobuxPrices() {
  const mongoUrl = process.env.MONGODB_URL
  if (!mongoUrl) {
    console.error("❌ MONGODB_URL environment variable is required")
    console.log("💡 Create a .env.local file with:")
    console.log("MONGODB_URL=mongodb://localhost:27017/bloxfruits")
    process.exit(1)
  }

  console.log("💎 TESTING ROBUX PRICE EXTRACTION")
  console.log("=================================")
  console.log("Testing accurate Robux price extraction for different item types")
  console.log("")

  const scraper = new FandomAPIScraper(mongoUrl)

  try {
    // Test avec des items qui ont des prix Robux connus
    const testItems = [
      { name: "Dragon", category: "fruit", expectedRobux: 3500 },
      { name: "<PERSON><PERSON>", category: "fruit", expectedRobux: 3000 },
      { name: "Venom", category: "fruit", expectedRobux: 2450 },
      { name: "Creation", category: "fruit", expectedRobux: 1750 },
      { name: "Dough", category: "fruit", expectedRobux: 2800 },
      { name: "Shadow", category: "fruit", expectedRobux: 2425 },
      // Items sans prix Robux
      { name: "Lei", category: "accessory", expectedRobux: null },
      { name: "Bazooka", category: "gun", expectedRobux: null },
      { name: "Bisento", category: "sword", expectedRobux: null },
      { name: "Blaze Ember", category: "material", expectedRobux: null }
    ]
    
    console.log(`🔍 Testing ${testItems.length} items for Robux price accuracy...`)
    
    let correctPrices = 0
    let incorrectPrices = 0
    let missingPrices = 0
    let unexpectedPrices = 0
    
    for (const testItem of testItems) {
      try {
        console.log(`\n📄 Testing ${testItem.name} (${testItem.category})...`)
        const item = await scraper.scrapeItem(testItem.name, testItem.category)
        
        if (item) {
          const extractedRobux = item.robuxPrice ? parseInt(item.robuxPrice.replace(/,/g, '')) : null
          
          console.log(`✅ ${testItem.name}: Successfully scraped!`)
          console.log(`   💰 Beli Price: ${item.price || 'N/A'}`)
          console.log(`   💎 Extracted Robux: ${extractedRobux || 'N/A'}`)
          console.log(`   🎯 Expected Robux: ${testItem.expectedRobux || 'N/A'}`)
          
          // Vérifier la précision
          if (testItem.expectedRobux === null) {
            // Item ne devrait pas avoir de prix Robux
            if (extractedRobux === null) {
              console.log(`   ✅ CORRECT: No Robux price (as expected)`)
              correctPrices++
            } else {
              console.log(`   ❌ UNEXPECTED: Found Robux price when none expected`)
              unexpectedPrices++
            }
          } else {
            // Item devrait avoir un prix Robux
            if (extractedRobux === testItem.expectedRobux) {
              console.log(`   ✅ CORRECT: Robux price matches expected value`)
              correctPrices++
            } else if (extractedRobux === null) {
              console.log(`   ❌ MISSING: No Robux price found`)
              missingPrices++
            } else {
              console.log(`   ⚠️ INCORRECT: Robux price doesn't match (got ${extractedRobux}, expected ${testItem.expectedRobux})`)
              incorrectPrices++
            }
          }
          
          // Afficher les données brutes pour debug
          if (item.rawData && item.rawData.infobox) {
            const infoboxRobux = item.rawData.infobox.robux
            if (infoboxRobux) {
              console.log(`   📊 Infobox Robux: ${infoboxRobux}`)
            }
          }
          
        } else {
          console.log(`❌ ${testItem.name}: Failed to scrape`)
        }
        
        // Pause entre les requêtes
        await new Promise(resolve => setTimeout(resolve, 1000))
        
      } catch (error) {
        console.log(`❌ ${testItem.name}: Error - ${error}`)
      }
    }
    
    console.log("\n📊 ROBUX PRICE EXTRACTION RESULTS:")
    console.log("==================================")
    console.log(`✅ Correct prices: ${correctPrices}`)
    console.log(`❌ Incorrect prices: ${incorrectPrices}`)
    console.log(`⚠️ Missing prices: ${missingPrices}`)
    console.log(`🚨 Unexpected prices: ${unexpectedPrices}`)
    
    const totalTests = correctPrices + incorrectPrices + missingPrices + unexpectedPrices
    const accuracy = totalTests > 0 ? ((correctPrices / totalTests) * 100).toFixed(1) : 0
    
    console.log(`📈 Accuracy: ${accuracy}% (${correctPrices}/${totalTests})`)
    
    if (accuracy >= 90) {
      console.log("🎉 EXCELLENT! Robux price extraction is highly accurate!")
    } else if (accuracy >= 70) {
      console.log("👍 GOOD! Robux price extraction is mostly accurate")
    } else {
      console.log("⚠️ NEEDS IMPROVEMENT: Robux price extraction needs refinement")
    }
    
    console.log("\n🔧 EXTRACTION METHODS TESTED:")
    console.log("=============================")
    console.log("1️⃣ Infobox robux field (highest priority)")
    console.log("2️⃣ {{Robux|X}} template patterns")
    console.log("3️⃣ 'costs X robux' text patterns")
    console.log("4️⃣ Description parsing for fruit prices")
    console.log("5️⃣ Smart filtering (only extract when expected)")
    
    console.log("\n💡 IMPROVEMENTS MADE:")
    console.log("====================")
    console.log("✅ Priority-based extraction (infobox first)")
    console.log("✅ Multiple pattern matching with regex reset")
    console.log("✅ Smart filtering based on item category")
    console.log("✅ Number parsing with comma handling")
    console.log("✅ Validation of positive values only")

  } catch (error) {
    console.error("❌ Error during Robux price testing:", error)
  }
  
  console.log("\n🏁 Robux price extraction testing completed!")
}

// Exécuter le test
testRobuxPrices().catch(console.error)
