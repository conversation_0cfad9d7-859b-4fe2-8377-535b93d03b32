import mongoose from "mongoose"

// Schéma pour les items génériques
const ItemSchema = new mongoose.Schema({
  name: { type: String, required: true, index: true },
  type: { type: String, required: true },
  category: {
    type: String,
    required: true,
    enum: ["fruit", "sword", "accessory", "gun", "material", "npc", "quest", "raid", "mechanic"],
    index: true,
  },
  rarity: { type: String, enum: ["Common", "Uncommon", "Rare", "Legendary", "Mythical"] },
  price: String,
  robuxPrice: String,
  damage: Number,
  mastery: Number,
  level: Number,
  location: String,
  description: String,
  stats: [String],
  moves: [
    {
      name: String,
      key: String,
      damage: Number,
      cooldown: Number,
      mastery: Number,
      energy: Number,
      type: String,
      description: String,
    },
  ],
  requirements: [String],
  rewards: [String],
  imageUrl: String,
  wikiUrl: { type: String, required: true },
  lastUpdated: { type: Date, default: Date.now },

  // Données spécialisées pour les matériaux
  materialData: {
    berryTypes: [String],
    locations: [{
      sea: String,
      location: String,
      bushes: Number
    }],
    usage: [{
      type: String,
      items: [{
        name: String,
        rarity: String,
        price: String
      }]
    }],
    totalRequired: mongoose.Schema.Types.Mixed,
    maxStack: Number,
    source: String,
    spawnRate: String,
    despawnTime: String
  },

  // Données spécialisées pour les fruits
  fruitData: {
    awakening: Boolean,
    type: {
      type: String,
      enum: ["Natural", "Elemental", "Beast", "Zoan", "Logia", "Paramecia"]
    },
    value: Number,
    stockChance: Number,
    spawnChance: Number,
    transformation: Boolean,
    passiveAbilities: [{
      name: String,
      description: String
    }],
    masteryRequirements: mongoose.Schema.Types.Mixed,
    combatRating: {
      pvp: {
        type: String,
        enum: ["Excellent", "Good", "Average", "Poor"]
      },
      grinding: {
        type: String,
        enum: ["Excellent", "Good", "Average", "Poor"]
      },
      raids: {
        type: String,
        enum: ["Excellent", "Good", "Average", "Poor"]
      }
    },
    pros: [String],
    cons: [String]
  },

  // Données spécialisées pour les armes
  weaponData: {
    damage: Number,
    masteryRequired: Number,
    upgradeRequirements: [{
      material: String,
      quantity: Number
    }],
    specialAbilities: [String]
  },

  // Données spécialisées pour les NPCs
  npcData: {
    npcType: {
      type: String,
      enum: ["Quest", "Shop", "Misc", "Boss", "Enemy"]
    },
    sea: Number,
    location: String,
    questRequirements: [{
      type: String,
      description: String,
      amount: Number
    }],
    questRewards: [{
      type: String,
      description: String,
      amount: Number
    }],
    questSteps: [String],
    dialogue: [String],
    cost: Number,
    services: [String]
  },

  // Données spécialisées pour les quêtes
  questData: {
    questGiver: String,
    requirements: [{
      type: String,
      description: String,
      amount: Number
    }],
    rewards: [{
      type: String,
      description: String,
      amount: Number
    }],
    steps: [String],
    difficulty: {
      type: String,
      enum: ["Easy", "Medium", "Hard", "Extreme"]
    },
    estimatedTime: String,
    tips: [String]
  },

  // Données spécialisées pour les ennemis/raids
  enemyData: {
    enemyType: {
      type: String,
      enum: ["Raid", "Boss", "Regular", "Elite"]
    },
    hp: Number,
    level: Number,
    baseAttack: Number,
    attacks: [{
      name: String,
      description: String,
      howToAvoid: String
    }],
    immunity: [String],
    aura: Boolean,
    weapon: String,
    spawnLocation: [String],
    behavior: String
  },

  rawData: mongoose.Schema.Types.Mixed,
})

// Schéma pour les valeurs de trading
const TradingValueSchema = new mongoose.Schema({
  itemName: { type: String, required: true, index: true },
  itemType: { type: String, required: true, enum: ["fruit", "sword", "accessory", "gun"] },
  currentValue: { type: Number, required: true },
  previousValue: { type: Number, required: true },
  trend: { type: String, enum: ["up", "down", "stable"], required: true },
  changePercent: { type: Number, required: true },
  confidence: { type: Number, min: 0, max: 100 },
  volume: { type: Number, default: 0 },
  lastUpdated: { type: Date, default: Date.now },
  source: { type: String, required: true },
})

// Schéma pour les mécaniques de jeu
const GameMechanicSchema = new mongoose.Schema({
  name: { type: String, required: true, index: true },
  type: {
    type: String,
    required: true,
    enum: ["combat", "leveling", "trading", "farming", "pvp", "raid"],
  },
  description: { type: String, required: true },
  formula: String,
  parameters: mongoose.Schema.Types.Mixed,
  examples: [String],
  lastUpdated: { type: Date, default: Date.now },
})

// Index composés pour optimiser les requêtes
ItemSchema.index({ category: 1, rarity: 1 })
ItemSchema.index({ name: "text", description: "text" })
TradingValueSchema.index({ itemName: 1, itemType: 1 }, { unique: true })

// Exporter les modèles
export const Item = mongoose.models.Item || mongoose.model("Item", ItemSchema)
export const TradingValue = mongoose.models.TradingValue || mongoose.model("TradingValue", TradingValueSchema)
export const GameMechanic = mongoose.models.GameMechanic || mongoose.model("GameMechanic", GameMechanicSchema)
