import dotenv from "dotenv"
import { FandomAPIScraper } from "../lib/scrapers/fandom-api-scraper"

// Charger les variables d'environnement
dotenv.config({ path: ".env.local" })

async function testNPCQuestEnemyScraper() {
  const mongoUrl = process.env.MONGODB_URL
  if (!mongoUrl) {
    console.error("❌ MONGODB_URL environment variable is required")
    console.log("💡 Create a .env.local file with:")
    console.log("MONGODB_URL=mongodb://localhost:27017/bloxfruits")
    process.exit(1)
  }

  console.log("🎭 TESTING NPC, QUEST & ENEMY SCRAPER")
  console.log("====================================")
  console.log("Testing enhanced scraper with NPC, Quest and Enemy data extraction")
  console.log("")

  const scraper = new FandomAPIScraper(mongoUrl)

  try {
    // Test NPCs
    console.log("👥 TESTING NPCs")
    console.log("===============")
    
    const testNPCs = ["Arowe", "Alchemist", "Wenlocktoad", "Dragon Wizard"]
    
    for (const npcName of testNPCs) {
      try {
        console.log(`\n📄 Testing ${npcName} NPC...`)
        const npcItem = await scraper.scrapeItem(npcName, "npc")
        
        if (npcItem) {
          console.log(`✅ ${npcName}: Successfully scraped!`)
          
          console.log(`   📝 Name: ${npcItem.name}`)
          console.log(`   🏷️ Category: ${npcItem.category}`)
          
          if (npcItem.npcData) {
            console.log(`   👤 NPC DATA:`)
            
            if (npcItem.npcData.npcType) {
              console.log(`     🎭 Type: ${npcItem.npcData.npcType}`)
            }
            
            if (npcItem.npcData.sea) {
              console.log(`     🌊 Sea: ${npcItem.npcData.sea}`)
            }
            
            if (npcItem.npcData.location) {
              console.log(`     📍 Location: ${npcItem.npcData.location}`)
            }
            
            if (npcItem.npcData.services && npcItem.npcData.services.length > 0) {
              console.log(`     🔧 Services: ${npcItem.npcData.services.join(", ")}`)
            }
            
            if (npcItem.npcData.questRequirements && npcItem.npcData.questRequirements.length > 0) {
              console.log(`     📋 Quest Requirements (${npcItem.npcData.questRequirements.length}):`)
              npcItem.npcData.questRequirements.slice(0, 3).forEach(req => {
                console.log(`       - ${req.type}: ${req.description.substring(0, 60)}...`)
                if (req.amount) console.log(`         💰 Amount: ${req.amount.toLocaleString()}`)
              })
            }
            
            if (npcItem.npcData.questSteps && npcItem.npcData.questSteps.length > 0) {
              console.log(`     🎯 Quest Steps (${npcItem.npcData.questSteps.length}):`)
              npcItem.npcData.questSteps.slice(0, 2).forEach(step => {
                console.log(`       - ${step.substring(0, 80)}...`)
              })
            }
            
            if (npcItem.npcData.dialogue && npcItem.npcData.dialogue.length > 0) {
              console.log(`     💬 Dialogue samples:`)
              npcItem.npcData.dialogue.slice(0, 2).forEach(dialogue => {
                console.log(`       "${dialogue}"`)
              })
            }
          }
          
        } else {
          console.log(`❌ ${npcName}: Failed to scrape`)
        }
        
        await new Promise(resolve => setTimeout(resolve, 1000))
        
      } catch (error) {
        console.log(`❌ ${npcName}: Error - ${error}`)
      }
    }

    // Test Quests
    console.log("\n\n🎯 TESTING QUESTS")
    console.log("=================")
    
    const testQuests = ["Alchemist", "Colosseum Quest"]
    
    for (const questName of testQuests) {
      try {
        console.log(`\n📄 Testing ${questName} Quest...`)
        const questItem = await scraper.scrapeItem(questName, "quest")
        
        if (questItem) {
          console.log(`✅ ${questName}: Successfully scraped!`)
          
          if (questItem.questData) {
            console.log(`   🎯 QUEST DATA:`)
            
            if (questItem.questData.questGiver) {
              console.log(`     👤 Quest Giver: ${questItem.questData.questGiver}`)
            }
            
            if (questItem.questData.difficulty) {
              console.log(`     ⭐ Difficulty: ${questItem.questData.difficulty}`)
            }
            
            if (questItem.questData.requirements && questItem.questData.requirements.length > 0) {
              console.log(`     📋 Requirements (${questItem.questData.requirements.length}):`)
              questItem.questData.requirements.forEach(req => {
                console.log(`       - ${req.type}: ${req.description}`)
                if (req.amount) console.log(`         💰 Amount: ${req.amount.toLocaleString()}`)
              })
            }
            
            if (questItem.questData.steps && questItem.questData.steps.length > 0) {
              console.log(`     🔄 Steps (${questItem.questData.steps.length}):`)
              questItem.questData.steps.slice(0, 3).forEach((step, index) => {
                console.log(`       ${index + 1}. ${step.substring(0, 80)}...`)
              })
            }
            
            if (questItem.questData.tips && questItem.questData.tips.length > 0) {
              console.log(`     💡 Tips (${questItem.questData.tips.length}):`)
              questItem.questData.tips.slice(0, 2).forEach(tip => {
                console.log(`       - ${tip.substring(0, 80)}...`)
              })
            }
          }
          
        } else {
          console.log(`❌ ${questName}: Failed to scrape`)
        }
        
        await new Promise(resolve => setTimeout(resolve, 1000))
        
      } catch (error) {
        console.log(`❌ ${questName}: Error - ${error}`)
      }
    }

    // Test Enemies/Raids
    console.log("\n\n⚔️ TESTING ENEMIES/RAIDS")
    console.log("========================")
    
    const testEnemies = ["Shocker", "Diamond", "Jeremy"]
    
    for (const enemyName of testEnemies) {
      try {
        console.log(`\n📄 Testing ${enemyName} Enemy...`)
        const enemyItem = await scraper.scrapeItem(enemyName, "enemy")
        
        if (enemyItem) {
          console.log(`✅ ${enemyName}: Successfully scraped!`)
          
          if (enemyItem.enemyData) {
            console.log(`   ⚔️ ENEMY DATA:`)
            
            if (enemyItem.enemyData.enemyType) {
              console.log(`     🎭 Type: ${enemyItem.enemyData.enemyType}`)
            }
            
            if (enemyItem.enemyData.hp) {
              console.log(`     ❤️ HP: ${enemyItem.enemyData.hp.toLocaleString()}`)
            }
            
            if (enemyItem.enemyData.level) {
              console.log(`     📊 Level: ${enemyItem.enemyData.level}`)
            }
            
            if (enemyItem.enemyData.baseAttack) {
              console.log(`     ⚔️ Base Attack: ${enemyItem.enemyData.baseAttack}`)
            }
            
            if (enemyItem.enemyData.aura !== undefined) {
              console.log(`     ✨ Aura: ${enemyItem.enemyData.aura ? "Yes" : "No"}`)
            }
            
            if (enemyItem.enemyData.weapon) {
              console.log(`     🗡️ Weapon: ${enemyItem.enemyData.weapon}`)
            }
            
            if (enemyItem.enemyData.spawnLocation && enemyItem.enemyData.spawnLocation.length > 0) {
              console.log(`     📍 Spawn Location: ${enemyItem.enemyData.spawnLocation.join(", ")}`)
            }
            
            if (enemyItem.enemyData.attacks && enemyItem.enemyData.attacks.length > 0) {
              console.log(`     💥 Attacks (${enemyItem.enemyData.attacks.length}):`)
              enemyItem.enemyData.attacks.forEach(attack => {
                console.log(`       - ${attack.name}`)
                console.log(`         📝 ${attack.description.substring(0, 60)}...`)
                console.log(`         🛡️ How to avoid: ${attack.howToAvoid.substring(0, 60)}...`)
              })
            }
            
            if (enemyItem.enemyData.behavior) {
              console.log(`     🎭 Behavior: ${enemyItem.enemyData.behavior.substring(0, 80)}...`)
            }
          }
          
        } else {
          console.log(`❌ ${enemyName}: Failed to scrape`)
        }
        
        await new Promise(resolve => setTimeout(resolve, 1000))
        
      } catch (error) {
        console.log(`❌ ${enemyName}: Error - ${error}`)
      }
    }
    
    console.log("\n📊 SUMMARY:")
    console.log("===========")
    console.log("Testing completed for NPCs, Quests, and Enemies.")
    console.log("The enhanced scraper now extracts:")
    console.log("✅ NPC information (type, location, services, dialogue)")
    console.log("✅ Quest requirements and steps")
    console.log("✅ Enemy stats and attack patterns")
    console.log("✅ Structured data for all categories")
    
  } catch (error) {
    console.error("❌ Error during testing:", error)
  }
  
  console.log("\n🎉 NPC/Quest/Enemy scraper testing completed!")
}

// Exécuter le test
testNPCQuestEnemyScraper().catch(console.error)
