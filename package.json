{"name": "blox-fruits-calculator", "version": "0.1.0", "private": true, "scripts": {"build": "next build", "dev": "next dev", "lint": "next lint", "start": "next start", "setup-db": "tsx scripts/setup-database.ts", "test-item": "tsx scripts/test-single-item.ts", "test-material": "tsx scripts/test-material-scraper.ts", "test-fruit": "tsx scripts/test-fruit-scraper.ts", "test-enhanced-fruit": "tsx scripts/test-enhanced-fruit-scraper.ts", "test-venom-complete": "tsx scripts/test-venom-complete.ts", "test-npc-quest-enemy": "tsx scripts/test-npc-quest-enemy-scraper.ts", "test-all-enhanced": "tsx scripts/test-all-enhanced-scraper.ts", "test-all-categories": "tsx scripts/test-all-categories-scraper.ts", "test-image-robux": "tsx scripts/test-image-robux-extraction.ts", "test-regex-fix": "tsx scripts/test-regex-fix.ts", "test-real-images": "tsx scripts/test-real-images.ts", "test-quick-images": "tsx scripts/test-quick-images.ts", "test-robux-prices": "tsx scripts/test-robux-prices.ts", "test-creation-fruit": "tsx scripts/test-creation-fruit.ts", "test-data-completeness": "tsx scripts/test-data-completeness.ts", "test-auto-categorization": "tsx scripts/test-auto-categorization.ts", "diagnostic-scraper": "tsx scripts/diagnostic-scraper.ts", "test-fixes": "tsx scripts/test-fixes.ts", "analyze-database": "tsx scripts/analyze-database.ts", "scrape-all": "tsx scripts/comprehensive-scraper.ts", "scrape-basic": "tsx scripts/run-scraper.ts", "cron-hourly": "bash scripts/run-hourly-scraper.sh"}, "dependencies": {"@aws-sdk/credential-providers": "latest", "@hookform/resolvers": "^3.9.1", "@mongodb-js/zstd": "latest", "@radix-ui/react-accordion": "1.2.2", "@radix-ui/react-alert-dialog": "1.1.4", "@radix-ui/react-aspect-ratio": "1.1.1", "@radix-ui/react-avatar": "latest", "@radix-ui/react-checkbox": "1.1.3", "@radix-ui/react-collapsible": "1.1.2", "@radix-ui/react-context-menu": "2.2.4", "@radix-ui/react-dialog": "1.1.4", "@radix-ui/react-dropdown-menu": "2.1.4", "@radix-ui/react-hover-card": "1.1.4", "@radix-ui/react-label": "2.1.1", "@radix-ui/react-menubar": "1.1.4", "@radix-ui/react-navigation-menu": "1.2.3", "@radix-ui/react-popover": "1.1.4", "@radix-ui/react-progress": "1.1.1", "@radix-ui/react-radio-group": "1.2.2", "@radix-ui/react-scroll-area": "1.2.2", "@radix-ui/react-select": "2.1.4", "@radix-ui/react-separator": "latest", "@radix-ui/react-slider": "1.2.2", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-switch": "latest", "@radix-ui/react-tabs": "1.1.2", "@radix-ui/react-toast": "1.2.4", "@radix-ui/react-toggle": "1.1.1", "@radix-ui/react-toggle-group": "1.1.1", "@radix-ui/react-tooltip": "1.1.6", "autoprefixer": "^10.4.20", "child_process": "latest", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "date-fns": "4.1.0", "dotenv": "latest", "embla-carousel-react": "8.5.1", "gcp-metadata": "latest", "input-otp": "1.4.1", "kerberos": "latest", "lucide-react": "^0.454.0", "mongodb": "latest", "mongodb-client-encryption": "latest", "mongoose": "latest", "next": "15.2.4", "next-themes": "^0.4.4", "node-fetch": "latest", "path": "latest", "react": "^19", "react-day-picker": "8.10.1", "react-dom": "^19", "react-hook-form": "^7.54.1", "react-resizable-panels": "^2.1.7", "recharts": "latest", "snappy": "latest", "socks": "latest", "sonner": "latest", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.6", "zod": "^3.24.1"}, "devDependencies": {"@types/node": "^22", "@types/react": "^19", "@types/react-dom": "^19", "postcss": "^8.5", "tailwindcss": "^3.4.17", "typescript": "^5"}}