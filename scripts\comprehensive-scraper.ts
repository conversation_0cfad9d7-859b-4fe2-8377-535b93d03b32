import dotenv from "dotenv"
import { FandomAPIScraper } from "../lib/scrapers/fandom-api-scraper"
import { MongoClient } from "mongodb"

// Charger les variables d'environnement
dotenv.config({ path: ".env.local" })

interface ScrapingStats {
  totalItems: number
  byCategory: Record<string, number>
  dataQuality: Record<string, number>
  errors: string[]
  duration: number
}

async function runComprehensiveScraper() {
  const mongoUrl = process.env.MONGODB_URL
  if (!mongoUrl) {
    console.error("❌ MONGODB_URL environment variable is required")
    console.log("💡 Create a .env.local file with:")
    console.log("MONGODB_URL=mongodb://localhost:27017/bloxfruits")
    console.log("or your MongoDB connection string")
    process.exit(1)
  }

  const startTime = Date.now()
  const stats: ScrapingStats = {
    totalItems: 0,
    byCategory: {},
    dataQuality: {},
    errors: [],
    duration: 0,
  }

  console.log("🚀 BLOX FRUITS COMPREHENSIVE SCRAPER")
  console.log("====================================")
  console.log("This will scrape ALL data from Blox Fruits Wiki with detailed statistics")
  console.log("")

  const scraper = new FandomAPIScraper(mongoUrl)
  const client = new MongoClient(mongoUrl)

  try {
    await client.connect()
    const db = client.db("bloxfruits")

    // Définir les catégories avec leurs collections correctes
    const categories = [
      { name: "Blox_Fruits", type: "fruit", collection: "fruits" },
      { name: "Swords", type: "sword", collection: "swords" },
      { name: "Materials", type: "material", collection: "materials" },
      { name: "NPCs", type: "npc", collection: "npcs" },
      { name: "Quests", type: "quest", collection: "quests" },
      { name: "Enemies", type: "enemy", collection: "enemies" },
      { name: "Accessories", type: "accessory", collection: "accessories" },
      { name: "Guns", type: "gun", collection: "guns" },
      { name: "Materials", type: "material", collection: "materials" },
      { name: "NPCs", type: "npc", collection: "npcs" },
      { name: "Quests", type: "quest", collection: "quests" },
      { name: "Raids", type: "raid", collection: "raids" },
      { name: "Game_Mechanics", type: "mechanic", collection: "mechanics" }, // Added mechanics category
    ]

    // Scraper chaque catégorie
    for (const category of categories) {
      console.log(`\n🔍 Processing category: ${category.name}`)

      try {
        const items = await scraper.scrapeCategory(category.name, category.type as any)

        if (items.length > 0) {
          const collection = db.collection(category.collection)

          // Supprimer les anciens items
          await collection.deleteMany({})

          // Insérer les nouveaux items
          await collection.insertMany(items)

          // Calculer la qualité des données
          const qualityScore = calculateCategoryQuality(items)

          stats.byCategory[category.type] = items.length
          stats.dataQuality[category.type] = qualityScore
          stats.totalItems += items.length

          console.log(`✅ ${category.type}: ${items.length} items (Quality: ${qualityScore}%)`)
        } else {
          console.warn(`⚠️ No items found for ${category.name}`)
          stats.errors.push(`No items found for ${category.name}`)
        }

        // Pause entre catégories
        await new Promise((resolve) => setTimeout(resolve, 2000))
      } catch (error) {
        console.error(`❌ Error processing ${category.name}:`, error)
        stats.errors.push(`${category.name}: ${error}`)
      }
    }

    stats.duration = Date.now() - startTime

    // Afficher les statistiques finales
    displayFinalStats(stats)

    // Sauvegarder les stats
    await saveScrapingStats(db, stats)

    // Envoyer notification Discord
    if (process.env.DISCORD_WEBHOOK_URL) {
      await sendDiscordNotification(stats)
    }
  } catch (error) {
    console.error("❌ Fatal error during comprehensive scraping:", error)
    stats.errors.push(`Fatal error: ${error}`)
  } finally {
    await client.close()
  }
}

function calculateCategoryQuality(items: any[]): number {
  if (items.length === 0) return 0

  let totalScore = 0

  items.forEach((item) => {
    let itemScore = 0

    // Champs de base (40 points)
    if (item.name) itemScore += 10
    if (item.type && item.type !== "Unknown") itemScore += 15
    if (item.category) itemScore += 15

    // Champs optionnels (60 points)
    if (item.rarity) itemScore += 8
    if (item.price || item.robuxPrice) itemScore += 8
    if (item.description && item.description.length > 50) itemScore += 10 // More points for longer description
    if (item.moves && item.moves.length > 0) itemScore += 10
    if (item.stats && item.stats.length > 0) itemScore += 8
    if (item.obtainment || item.upgrading || item.location) itemScore += 6 // New fields
    if (item.imageUrl) itemScore += 5 // Image URL

    totalScore += itemScore
  })

  return Math.round(totalScore / items.length)
}

function displayFinalStats(stats: ScrapingStats) {
  console.log("\n" + "=".repeat(50))
  console.log("📊 COMPREHENSIVE SCRAPING RESULTS")
  console.log("=".repeat(50))

  console.log(`⏱️  Duration: ${Math.round(stats.duration / 1000)}s`)
  console.log(`📦 Total Items: ${stats.totalItems}`)

  console.log("\n📋 By Category:")
  Object.entries(stats.byCategory).forEach(([category, count]) => {
    const quality = stats.dataQuality[category] || 0
    console.log(`  ${category.padEnd(12)}: ${count.toString().padStart(3)} items (${quality}% quality)`)
  })

  if (stats.errors.length > 0) {
    console.log("\n❌ Errors:")
    stats.errors.forEach((error) => console.log(`  - ${error}`))
  }

  const avgQuality =
    Object.values(stats.dataQuality).reduce((a, b) => a + b, 0) / Object.values(stats.dataQuality).length
  console.log(`\n🎯 Average Data Quality: ${Math.round(avgQuality)}%`)
}

async function saveScrapingStats(db: any, stats: ScrapingStats) {
  try {
    const collection = db.collection("scraping_stats")
    await collection.insertOne({
      ...stats,
      timestamp: new Date(),
      version: "2.2", // Updated version
    })
    console.log("💾 Scraping stats saved to database")
  } catch (error) {
    console.error("Failed to save scraping stats:", error)
  }
}

async function sendDiscordNotification(stats: ScrapingStats) {
  try {
    const webhook = process.env.DISCORD_WEBHOOK_URL!
    const avgQuality =
      Object.values(stats.dataQuality).reduce((a, b) => a + b, 0) / Object.values(stats.dataQuality).length

    const embed = {
      title: "🎮 Blox Fruits Database Updated",
      description: `Comprehensive scraping completed with enhanced data extraction.`,
      color: stats.errors.length > 0 ? 0xff9900 : 0x00ff00,
      fields: [
        {
          name: "📊 Total Items",
          value: stats.totalItems.toString(),
          inline: true,
        },
        {
          name: "⏱️ Duration",
          value: `${Math.round(stats.duration / 1000)}s`,
          inline: true,
        },
        {
          name: "🎯 Avg Quality",
          value: `${Math.round(avgQuality)}%`,
          inline: true,
        },
        {
          name: "📋 Breakdown",
          value: Object.entries(stats.byCategory)
            .map(([cat, count]) => `${cat}: ${count}`)
            .join("\n"),
          inline: false,
        },
      ],
      footer: {
        text: "Blox Fruits Calculator - Enhanced Scraper v2.2",
      },
      timestamp: new Date().toISOString(),
    }

    if (stats.errors.length > 0) {
      embed.fields.push({
        name: "⚠️ Errors",
        value:
          stats.errors.slice(0, 3).join("\n") +
          (stats.errors.length > 3 ? `\n... and ${stats.errors.length - 3} more` : ""),
        inline: false,
      })
    }

    await fetch(webhook, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ embeds: [embed] }),
    })

    console.log("📢 Discord notification sent!")
  } catch (error) {
    console.error("Failed to send Discord notification:", error)
  }
}

// Gestion des signaux pour arrêt propre
process.on("SIGINT", () => {
  console.log("\n⏹️ Comprehensive scraping interrupted by user")
  process.exit(0)
})

runComprehensiveScraper().catch(console.error)
