import dotenv from "dotenv"
import { FandomAPIScraper } from "../lib/scrapers/fandom-api-scraper"

// Charger les variables d'environnement
dotenv.config({ path: ".env.local" })

async function testFruitScraper() {
  const mongoUrl = process.env.MONGODB_URL
  if (!mongoUrl) {
    console.error("❌ MONGODB_URL environment variable is required")
    console.log("💡 Create a .env.local file with:")
    console.log("MONGODB_URL=mongodb://localhost:27017/bloxfruits")
    process.exit(1)
  }

  console.log("🍎 TESTING FRUIT SCRAPER")
  console.log("========================")
  console.log("Testing enhanced scraper with fruit data extraction")
  console.log("")

  const scraper = new FandomAPIScraper(mongoUrl)

  try {
    // Test avec différents fruits populaires
    const testFruits = [
      "<PERSON>",
      "<PERSON><PERSON>", 
      "<PERSON><PERSON><PERSON>",
      "<PERSON>",
      "<PERSON><PERSON>",
      "Shadow",
      "<PERSON>enom",
      "<PERSON>",
      "Spirit",
      "Flame"
    ]
    
    console.log(`🔍 Testing ${testFruits.length} popular fruits...`)
    
    for (const fruitName of testFruits) {
      try {
        console.log(`\n📄 Testing ${fruitName} fruit...`)
        const fruitItem = await scraper.scrapeItem(fruitName, "fruit")
        
        if (fruitItem) {
          console.log(`✅ ${fruitName}: Successfully scraped!`)
          
          // Afficher les informations de base
          console.log(`   📝 Name: ${fruitItem.name}`)
          console.log(`   🏷️ Category: ${fruitItem.category}`)
          console.log(`   ⭐ Rarity: ${fruitItem.rarity || "Unknown"}`)
          console.log(`   💰 Price: ${fruitItem.price || "Unknown"}`)
          console.log(`   💎 Robux Price: ${fruitItem.robuxPrice || "N/A"}`)
          
          if (fruitItem.description) {
            console.log(`   📖 Description: ${fruitItem.description.substring(0, 80)}...`)
          }
          
          // Afficher les données spécifiques aux fruits
          if (fruitItem.fruitData) {
            console.log(`   🍎 FRUIT DATA:`)
            
            if (fruitItem.fruitData.type) {
              console.log(`     🔥 Type: ${fruitItem.fruitData.type}`)
            }
            
            if (fruitItem.fruitData.awakening !== undefined) {
              console.log(`     ⚡ Awakening: ${fruitItem.fruitData.awakening ? "Available" : "Not Available"}`)
            }
            
            if (fruitItem.fruitData.value) {
              console.log(`     💰 Value: ${fruitItem.fruitData.value.toLocaleString()}`)
            }
            
            if (fruitItem.fruitData.stockChance) {
              console.log(`     🏪 Stock Chance: ${fruitItem.fruitData.stockChance}%`)
            }
            
            if (fruitItem.fruitData.spawnChance) {
              console.log(`     🌍 Spawn Chance: ${fruitItem.fruitData.spawnChance}%`)
            }
          }
          
          // Afficher les moves si disponibles
          if (fruitItem.moves && fruitItem.moves.length > 0) {
            console.log(`   ⚔️ Moves (${fruitItem.moves.length}):`)
            fruitItem.moves.slice(0, 3).forEach(move => {
              console.log(`     - ${move.name}${move.key ? ` [${move.key}]` : ""}`)
              if (move.damage) console.log(`       💥 Damage: ${move.damage}`)
              if (move.cooldown) console.log(`       ⏰ Cooldown: ${move.cooldown}s`)
              if (move.mastery) console.log(`       🎯 Mastery: ${move.mastery}`)
            })
            if (fruitItem.moves.length > 3) {
              console.log(`     ... and ${fruitItem.moves.length - 3} more moves`)
            }
          }
          
          // Afficher les stats si disponibles
          if (fruitItem.stats && fruitItem.stats.length > 0) {
            console.log(`   📊 Stats: ${fruitItem.stats.join(", ")}`)
          }
          
        } else {
          console.log(`❌ ${fruitName}: Failed to scrape`)
        }
        
        // Petite pause entre les requêtes
        await new Promise(resolve => setTimeout(resolve, 1000))
        
      } catch (error) {
        console.log(`❌ ${fruitName}: Error - ${error}`)
      }
    }
    
    console.log("\n📊 SUMMARY:")
    console.log("===========")
    console.log("Testing completed for all fruits.")
    console.log("The enhanced scraper now extracts:")
    console.log("✅ Basic fruit information (name, rarity, price)")
    console.log("✅ Fruit-specific data (type, awakening, value)")
    console.log("✅ Spawn and stock chances")
    console.log("✅ Moves and abilities")
    console.log("✅ Stats and requirements")
    console.log("✅ Raw data for analysis")
    
  } catch (error) {
    console.error("❌ Error during testing:", error)
  }
  
  console.log("\n🎉 Fruit scraper testing completed!")
}

// Exécuter le test
testFruitScraper().catch(console.error)
