import dotenv from "dotenv"
import { MongoClient } from "mongodb"

// Charger les variables d'environnement
dotenv.config({ path: ".env.local" })

interface DatabaseItem {
  _id: any
  name: string
  type: string
  category: string
  rarity?: string
  price?: string
  robuxPrice?: string
  level?: number
  moves?: Array<any>
  stats?: Array<any>
  weaponData?: any
  fruitData?: any
  npcData?: any
  enemyData?: any
  materialData?: any
  accessoryData?: any
  mechanicData?: any
  questData?: any
  rawData?: any
  imageUrls?: Array<string>
  lastUpdated?: Date
}

async function analyzeDatabaseInconsistencies() {
  const mongoUrl = process.env.MONGODB_URL
  if (!mongoUrl) {
    console.error("❌ MONGODB_URL environment variable is required")
    process.exit(1)
  }

  console.log("🔍 ANALYSE DES INCOHÉRENCES DE LA BASE DE DONNÉES")
  console.log("================================================")
  console.log("Lecture directe de la base de données pour identifier les problèmes")
  console.log("")

  const client = new MongoClient(mongoUrl)

  try {
    await client.connect()
    const db = client.db("bloxfruits")
    
    // Obtenir toutes les collections
    const collections = await db.listCollections().toArray()
    console.log(`📊 Collections trouvées: ${collections.map(c => c.name).join(', ')}`)
    
    // Analyser toutes les collections avec des données
    const collectionsWithData = []

    for (const collection of collections) {
      const count = await db.collection(collection.name).countDocuments()
      if (count > 0) {
        collectionsWithData.push({
          name: collection.name,
          count: count,
          collection: db.collection(collection.name)
        })
      }
    }

    if (collectionsWithData.length === 0) {
      console.log("❌ Aucune donnée trouvée dans aucune collection")
      return
    }

    console.log(`📊 Collections avec données trouvées: ${collectionsWithData.length}`)
    collectionsWithData.forEach(col => {
      console.log(`   - ${col.name}: ${col.count} documents`)
    })

    // Analyser chaque collection avec des données
    const collectionSummaries = []

    for (const collectionInfo of collectionsWithData) {
      console.log(`\n🔍 Analyse de ${collectionInfo.name}...`)
      const summary = await analyzeCollection(collectionInfo.collection, collectionInfo.name)
      collectionSummaries.push({
        name: collectionInfo.name,
        count: collectionInfo.count,
        summary: summary
      })
      console.log("\n" + "=".repeat(80) + "\n")
    }

    // Résumé global de toutes les collections
    console.log("🌍 RÉSUMÉ GLOBAL DE LA BASE DE DONNÉES")
    console.log("=====================================")

    const totalItems = collectionsWithData.reduce((sum, col) => sum + col.count, 0)
    console.log(`📊 Total d'items dans la base: ${totalItems.toLocaleString()}`)
    console.log(`📂 Collections actives: ${collectionsWithData.length}`)

    console.log("\n📈 RÉPARTITION PAR COLLECTION:")
    collectionsWithData
      .sort((a, b) => b.count - a.count)
      .forEach(col => {
        const percentage = totalItems > 0 ? ((col.count / totalItems) * 100).toFixed(1) : '0.0'
        console.log(`   ${col.name}: ${col.count.toLocaleString()} items (${percentage}%)`)
      })

    console.log("\n🎯 RECOMMANDATIONS GLOBALES:")
    console.log("============================")

    if (totalItems === 0) {
      console.log("❌ CRITIQUE: Aucune donnée dans la base")
      console.log("   💡 Exécuter le scraper: npm run comprehensive-scraper")
    } else if (totalItems < 100) {
      console.log("⚠️ ATTENTION: Peu de données disponibles")
      console.log("   💡 Scraper plus de catégories pour enrichir la base")
    } else {
      console.log("✅ BIEN: Base de données bien fournie")
      console.log("   💡 Maintenir la qualité avec des mises à jour régulières")
    }

    console.log("\n🔧 ACTIONS RECOMMANDÉES:")
    console.log("========================")
    console.log("1. 🔄 Mise à jour régulière: npm run comprehensive-scraper")
    console.log("2. 🔍 Monitoring qualité: npm run analyze-database")
    console.log("3. 🧪 Tests de validation: npm run final-validation")
    console.log("4. 📊 Vérification code: npm run verify-scraper-code")

  } catch (error) {
    console.error("❌ Erreur lors de l'analyse:", error)
  } finally {
    await client.close()
  }
}

async function analyzeCollection(collection: any, collectionName: string): Promise<any> {
  console.log(`\n🔍 ANALYSE DE LA COLLECTION '${collectionName.toUpperCase()}'`)
  console.log("=" + "=".repeat(collectionName.length + 25))

  // Obtenir un échantillon pour comprendre la structure
  const sampleDoc = await collection.findOne({})
  const hasCategory = sampleDoc && 'category' in sampleDoc
  const hasType = sampleDoc && 'type' in sampleDoc
  const hasName = sampleDoc && 'name' in sampleDoc

  console.log(`📋 Structure détectée: ${hasName ? '✅ name' : '❌ name'}, ${hasCategory ? '✅ category' : '❌ category'}, ${hasType ? '✅ type' : '❌ type'}`)

  // 1. Analyse des catégories (si disponible)
  if (hasCategory) {
    console.log("\n📂 ANALYSE DES CATÉGORIES:")
    const categoryStats = await collection.aggregate([
      { $group: { _id: "$category", count: { $sum: 1 } } },
      { $sort: { count: -1 } }
    ]).toArray()

    categoryStats.forEach(stat => {
      console.log(`   ${stat._id || 'undefined'}: ${stat.count} items`)
    })
  } else {
    console.log("\n📂 CATÉGORIES: Non disponibles dans cette collection")
  }
  
  // 2. Analyse des types vs catégories (si disponible)
  let inconsistencies: string[] = []

  if (hasCategory && hasType) {
    console.log("\n🏷️ ANALYSE TYPE VS CATÉGORIE:")
    const typeVsCategory = await collection.aggregate([
      { $group: { _id: { category: "$category", type: "$type" }, count: { $sum: 1 } } },
      { $sort: { "_id.category": 1, count: -1 } }
    ]).toArray()

    typeVsCategory.forEach(stat => {
      const category = stat._id.category
      const type = stat._id.type
      const count = stat.count

      console.log(`   ${category || 'undefined'} → ${type || 'undefined'}: ${count} items`)

      // Détecter les incohérences
      if (category === "sword" && type !== "Sword") {
        inconsistencies.push(`❌ Catégorie 'sword' avec type '${type}' (${count} items)`)
      }
      if (category === "fruit" && !["Paramecia", "Logia", "Zoan", "Beast", "Natural"].includes(type)) {
        inconsistencies.push(`❌ Catégorie 'fruit' avec type '${type}' (${count} items)`)
      }
      if (category === "enemy" && type !== "Enemy") {
        inconsistencies.push(`❌ Catégorie 'enemy' avec type '${type}' (${count} items)`)
      }
      if (category === "npc" && !["Misc", "NPC", "Quest", "Shop"].includes(type)) {
        inconsistencies.push(`❌ Catégorie 'npc' avec type '${type}' (${count} items)`)
      }
      if (category === "gun" && type !== "Gun") {
        inconsistencies.push(`❌ Catégorie 'gun' avec type '${type}' (${count} items)`)
      }
    })
  } else {
    console.log("\n🏷️ TYPES VS CATÉGORIES: Non disponibles dans cette collection")
  }
  
  // 3. Analyse des données manquantes
  console.log("\n📊 ANALYSE DES DONNÉES MANQUANTES:")

  // Construire dynamiquement la projection basée sur les champs disponibles
  const projection: any = { name: 1 }

  if (hasCategory) projection.category = 1

  // Vérifier quels champs existent dans la collection
  const fieldChecks = [
    { field: "price", name: "hasPrice" },
    { field: "robuxPrice", name: "hasRobuxPrice" },
    { field: "imageUrls", name: "hasImages", isArray: true },
    { field: "moves", name: "hasMoves", isArray: true },
    { field: "weaponData", name: "hasWeaponData" },
    { field: "fruitData", name: "hasFruitData" },
    { field: "npcData", name: "hasNpcData" },
    { field: "enemyData", name: "hasEnemyData" },
    { field: "materialData", name: "hasMaterialData" },
    { field: "accessoryData", name: "hasAccessoryData" },
    { field: "questData", name: "hasQuestData" },
    { field: "mechanicData", name: "hasMechanicData" }
  ]

  // Ajouter les projections pour les champs qui existent
  fieldChecks.forEach(check => {
    if (sampleDoc && check.field in sampleDoc) {
      if (check.isArray) {
        projection[check.name] = { $cond: [{ $gt: [{ $size: { $ifNull: [`$${check.field}`, []] } }, 0] }, 1, 0] }
      } else {
        projection[check.name] = { $cond: [{ $ne: [`$${check.field}`, null] }, 1, 0] }
      }
    }
  })

  const groupBy = hasCategory ? "$category" : "all"
  const missingData = await collection.aggregate([
    { $project: projection },
    {
      $group: {
        _id: groupBy,
        count: { $sum: 1 },
        ...Object.keys(projection).reduce((acc, key) => {
          if (key.startsWith('has')) {
            acc[`with${key.substring(3)}`] = { $sum: `$${key}` }
          }
          return acc
        }, {} as any)
      }
    }
  ]).toArray()
  
  missingData.forEach(stat => {
    const category = stat._id || 'Collection'
    const total = stat.count

    console.log(`\n   📂 ${category} (${total} items):`)

    // Afficher dynamiquement tous les champs disponibles
    Object.keys(stat).forEach(key => {
      if (key.startsWith('with') && key !== 'withCategory' && key !== 'withName') {
        const fieldName = key.substring(4) // Enlever 'with'
        const count = stat[key] || 0
        const percentage = total > 0 ? ((count/total)*100).toFixed(1) : '0.0'

        // Icônes pour différents types de données
        const icons: { [key: string]: string } = {
          'Price': '💰',
          'RobuxPrice': '💎',
          'Images': '🖼️',
          'Moves': '⚔️',
          'WeaponData': '🔫',
          'FruitData': '🍎',
          'NpcData': '👤',
          'EnemyData': '👹',
          'MaterialData': '🧱',
          'AccessoryData': '💍',
          'QuestData': '📋',
          'MechanicData': '⚙️'
        }

        const icon = icons[fieldName] || '📊'
        console.log(`     ${icon} ${fieldName}: ${count}/${total} (${percentage}%)`)
      }
    })

    // Calculer un score de complétude global
    const dataFields = Object.keys(stat).filter(key => key.startsWith('with') && key !== 'withCategory' && key !== 'withName')
    if (dataFields.length > 0) {
      const totalPossible = dataFields.length * total
      const totalPresent = dataFields.reduce((sum, key) => sum + (stat[key] || 0), 0)
      const completenessScore = totalPossible > 0 ? ((totalPresent / totalPossible) * 100).toFixed(1) : '0.0'
      console.log(`     📈 Score de complétude: ${completenessScore}%`)
    }
  })
  
  // 4. Exemples d'items problématiques
  console.log("\n🚨 EXEMPLES D'ITEMS PROBLÉMATIQUES:")

  // Construire dynamiquement les conditions problématiques
  const problematicConditions: any[] = []

  if (hasCategory && hasType) {
    problematicConditions.push(
      { category: "sword", type: { $ne: "Sword" } },
      { category: "fruit", type: { $nin: ["Paramecia", "Logia", "Zoan", "Beast", "Natural"] } },
      { category: "enemy", type: { $ne: "Enemy" } },
      { category: "gun", type: { $ne: "Gun" } },
      { category: "npc", type: { $nin: ["Misc", "NPC", "Quest", "Shop"] } }
    )
  }

  // Ajouter des conditions pour les données manquantes
  if (sampleDoc) {
    if ('npcData' in sampleDoc) problematicConditions.push({ category: "npc", npcData: null })
    if ('weaponData' in sampleDoc) problematicConditions.push({ category: { $in: ["sword", "gun"] }, weaponData: null })
    if ('fruitData' in sampleDoc) problematicConditions.push({ category: "fruit", fruitData: null })
    if ('enemyData' in sampleDoc) problematicConditions.push({ category: "enemy", enemyData: null })
    if ('materialData' in sampleDoc) problematicConditions.push({ category: "material", materialData: null })
  }

  if (problematicConditions.length > 0) {
    const problematicItems = await collection.find({
      $or: problematicConditions
    }).limit(10).toArray()

    if (problematicItems.length > 0) {
      problematicItems.forEach((item: DatabaseItem) => {
        console.log(`   ❌ ${item.name || 'Nom manquant'}:`)
        if (hasCategory && hasType) {
          console.log(`     - Catégorie: ${item.category || 'N/A'}, Type: ${item.type || 'N/A'}`)
        }

        // Identifier les problèmes spécifiques
        if (item.category === "npc" && !item.npcData) {
          console.log(`     - Problème: NPC sans npcData`)
        }
        if ((item.category === "sword" || item.category === "gun") && !item.weaponData) {
          console.log(`     - Problème: Arme sans weaponData`)
        }
        if (item.category === "fruit" && !item.fruitData) {
          console.log(`     - Problème: Fruit sans fruitData`)
        }
        if (item.category === "enemy" && !item.enemyData) {
          console.log(`     - Problème: Ennemi sans enemyData`)
        }
        if (item.category === "material" && !item.materialData) {
          console.log(`     - Problème: Matériau sans materialData`)
        }
      })
    } else {
      console.log("   ✅ Aucun item problématique détecté!")
    }
  } else {
    console.log("   ℹ️ Impossible d'analyser les items problématiques (structure de données insuffisante)")
  }
  
  // 5. Résumé des incohérences
  console.log("\n📋 RÉSUMÉ DES INCOHÉRENCES:")
  if (hasCategory && hasType && inconsistencies && inconsistencies.length > 0) {
    inconsistencies.forEach(inc => console.log(`   ${inc}`))
  } else {
    console.log("   ✅ Aucune incohérence majeure détectée")
  }

  // 6. Recommandations spécifiques à cette collection
  console.log("\n💡 RECOMMANDATIONS POUR CETTE COLLECTION:")

  // Recommandations basées sur l'analyse
  let recommendations: string[] = []

  if (hasCategory && hasType && inconsistencies && inconsistencies.length > 0) {
    recommendations.push("🔧 Corriger les catégories/types incohérents")
  }

  // Analyser les taux de complétude pour des recommandations spécifiques
  missingData.forEach(stat => {
    const total = stat.count
    Object.keys(stat).forEach(key => {
      if (key.startsWith('with')) {
        const count = stat[key] || 0
        const percentage = total > 0 ? (count/total)*100 : 0
        const fieldName = key.substring(4)

        if (percentage < 50) {
          recommendations.push(`📊 Améliorer l'extraction de ${fieldName} (${percentage.toFixed(1)}%)`)
        }
      }
    })
  })

  if (recommendations.length > 0) {
    recommendations.slice(0, 8).forEach(rec => console.log(`   ${rec}`))
    if (recommendations.length > 8) {
      console.log(`   ... et ${recommendations.length - 8} autres recommandations`)
    }
  } else {
    console.log("   🎉 Cette collection semble bien structurée!")
  }

  // 7. Score global de la collection
  console.log("\n📈 SCORE GLOBAL DE LA COLLECTION:")
  const totalItems = missingData.reduce((sum, stat) => sum + stat.count, 0)
  const allDataFields = missingData.flatMap(stat =>
    Object.keys(stat).filter(key => key.startsWith('with') && key !== 'withCategory' && key !== 'withName')
  )
  const uniqueDataFields = [...new Set(allDataFields)]

  if (uniqueDataFields.length > 0 && totalItems > 0) {
    const totalPossible = uniqueDataFields.length * totalItems
    const totalPresent = missingData.reduce((sum, stat) =>
      sum + uniqueDataFields.reduce((fieldSum, field) => fieldSum + (stat[field] || 0), 0), 0
    )
    const globalScore = totalPossible > 0 ? ((totalPresent / totalPossible) * 100).toFixed(1) : '0.0'

    console.log(`   📊 Score de complétude global: ${globalScore}%`)
    console.log(`   📄 Total d'items: ${totalItems}`)
    console.log(`   🔢 Champs de données: ${uniqueDataFields.length}`)

    if (parseFloat(globalScore) >= 80) {
      console.log(`   🟢 EXCELLENT: Collection bien remplie`)
    } else if (parseFloat(globalScore) >= 60) {
      console.log(`   🟡 BIEN: Collection correctement remplie`)
    } else {
      console.log(`   🔴 ATTENTION: Collection nécessite des améliorations`)
    }
  } else {
    console.log(`   📊 Score: Non calculable (structure de données insuffisante)`)
  }

  // Retourner un résumé pour l'analyse globale
  return {
    hasCategory,
    hasType,
    hasName,
    totalItems: missingData.reduce((sum, stat) => sum + stat.count, 0),
    categories: hasCategory ? missingData.map(stat => stat._id) : [],
    inconsistencies: (typeof inconsistencies !== 'undefined') ? inconsistencies : [],
    recommendations: (typeof recommendations !== 'undefined') ? recommendations : []
  }
}

// Exécuter l'analyse
analyzeDatabaseInconsistencies().catch(console.error)
