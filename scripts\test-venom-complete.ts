import dotenv from "dotenv"
import { FandomAPIScraper } from "../lib/scrapers/fandom-api-scraper"

// Charger les variables d'environnement
dotenv.config({ path: ".env.local" })

async function testVenomComplete() {
  const mongoUrl = process.env.MONGODB_URL
  if (!mongoUrl) {
    console.error("❌ MONGODB_URL environment variable is required")
    console.log("💡 Create a .env.local file with:")
    console.log("MONGODB_URL=mongodb://localhost:27017/bloxfruits")
    process.exit(1)
  }

  console.log("🐍 COMPLETE VENOM FRUIT ANALYSIS")
  console.log("================================")
  console.log("Testing comprehensive data extraction from Venom fruit")
  console.log("")

  const scraper = new FandomAPIScraper(mongoUrl)

  try {
    console.log("🔍 Extracting Venom fruit data...")
    const venomItem = await scraper.scrapeItem("Venom", "fruit")
    
    if (venomItem) {
      console.log("✅ Venom fruit successfully scraped!")
      console.log("")
      
      // Informations de base
      console.log("📋 BASIC INFORMATION:")
      console.log("=====================")
      console.log(`📝 Name: ${venomItem.name}`)
      console.log(`🏷️ Category: ${venomItem.category}`)
      console.log(`⭐ Rarity: ${venomItem.rarity}`)
      console.log(`💰 Price: ${venomItem.price}`)
      console.log(`💎 Robux Price: ${venomItem.robuxPrice}`)
      console.log(`📖 Description: ${venomItem.description?.substring(0, 150)}...`)
      
      if (venomItem.fruitData) {
        console.log("\n🍎 FRUIT DATA ANALYSIS:")
        console.log("=======================")
        
        // Type et capacités de base
        console.log(`🔥 Type: ${venomItem.fruitData.type}`)
        console.log(`⚡ Awakening: ${venomItem.fruitData.awakening ? "Available" : "Not Available"}`)
        console.log(`🔄 Transformation: ${venomItem.fruitData.transformation ? "Yes" : "No"}`)
        console.log(`💰 Value: ${venomItem.fruitData.value?.toLocaleString()}`)
        
        // Capacités passives
        if (venomItem.fruitData.passiveAbilities && venomItem.fruitData.passiveAbilities.length > 0) {
          console.log(`\n🌟 PASSIVE ABILITIES (${venomItem.fruitData.passiveAbilities.length}):`)
          venomItem.fruitData.passiveAbilities.forEach((passive, index) => {
            console.log(`   ${index + 1}. ${passive.name}`)
            console.log(`      📝 ${passive.description}`)
          })
        }
        
        // Exigences de maîtrise
        if (venomItem.fruitData.masteryRequirements) {
          console.log(`\n🎯 MASTERY REQUIREMENTS:`)
          Object.entries(venomItem.fruitData.masteryRequirements).forEach(([move, mastery]) => {
            console.log(`   • ${move}: ${mastery} mastery`)
          })
        }
        
        // Ratings de combat
        if (venomItem.fruitData.combatRating) {
          console.log(`\n⚔️ COMBAT RATINGS:`)
          if (venomItem.fruitData.combatRating.pvp) {
            console.log(`   🥊 PvP: ${venomItem.fruitData.combatRating.pvp}`)
          }
          if (venomItem.fruitData.combatRating.grinding) {
            console.log(`   🌾 Grinding: ${venomItem.fruitData.combatRating.grinding}`)
          }
          if (venomItem.fruitData.combatRating.raids) {
            console.log(`   🏴‍☠️ Raids: ${venomItem.fruitData.combatRating.raids}`)
          }
        }
        
        // Pros
        if (venomItem.fruitData.pros && venomItem.fruitData.pros.length > 0) {
          console.log(`\n✅ ADVANTAGES (${venomItem.fruitData.pros.length}):`)
          venomItem.fruitData.pros.forEach((pro, index) => {
            console.log(`   ${index + 1}. ${pro}`)
          })
        }
        
        // Cons
        if (venomItem.fruitData.cons && venomItem.fruitData.cons.length > 0) {
          console.log(`\n❌ DISADVANTAGES (${venomItem.fruitData.cons.length}):`)
          venomItem.fruitData.cons.forEach((con, index) => {
            console.log(`   ${index + 1}. ${con}`)
          })
        }
        
        // Recommandations
        if (venomItem.fruitData.recommendations && venomItem.fruitData.recommendations.length > 0) {
          console.log(`\n💡 RECOMMENDATIONS (${venomItem.fruitData.recommendations.length}):`)
          venomItem.fruitData.recommendations.forEach((rec, index) => {
            console.log(`   ${index + 1}. ${rec}`)
          })
        }
        
        // Counters
        if (venomItem.fruitData.counters && venomItem.fruitData.counters.length > 0) {
          console.log(`\n🎯 COUNTERS (${venomItem.fruitData.counters.length}):`)
          venomItem.fruitData.counters.forEach((counter, index) => {
            console.log(`   ${index + 1}. ${counter}`)
          })
        }
        
        // Trivia
        if (venomItem.fruitData.trivia && venomItem.fruitData.trivia.length > 0) {
          console.log(`\n📚 TRIVIA & FUN FACTS (${venomItem.fruitData.trivia.length}):`)
          venomItem.fruitData.trivia.slice(0, 8).forEach((trivia, index) => {
            console.log(`   ${index + 1}. ${trivia}`)
          })
          if (venomItem.fruitData.trivia.length > 8) {
            console.log(`   ... and ${venomItem.fruitData.trivia.length - 8} more facts`)
          }
        }
        
        // Historique des changements
        if (venomItem.fruitData.changeHistory && venomItem.fruitData.changeHistory.length > 0) {
          console.log(`\n📝 CHANGE HISTORY (${venomItem.fruitData.changeHistory.length} updates):`)
          venomItem.fruitData.changeHistory.forEach((change, index) => {
            console.log(`   🔄 Update ${change.update}:`)
            change.changes.forEach(changeDetail => {
              console.log(`      - ${changeDetail}`)
            })
          })
        }
      }
      
      // Moves
      if (venomItem.moves && venomItem.moves.length > 0) {
        console.log(`\n⚔️ MOVESET (${venomItem.moves.length} moves):`)
        venomItem.moves.forEach((move, index) => {
          console.log(`   ${index + 1}. ${move.name}${move.key ? ` [${move.key}]` : ""}`)
          if (move.description) {
            console.log(`      📝 ${move.description.substring(0, 100)}...`)
          }
          if (move.mastery) console.log(`      🎯 Mastery: ${move.mastery}`)
          if (move.damage) console.log(`      💥 Damage: ${move.damage}`)
          if (move.cooldown) console.log(`      ⏰ Cooldown: ${move.cooldown}s`)
        })
      }
      
      console.log("\n📊 DATA EXTRACTION SUMMARY:")
      console.log("===========================")
      console.log(`✅ Basic Info: Complete`)
      console.log(`✅ Passive Abilities: ${venomItem.fruitData?.passiveAbilities?.length || 0} extracted`)
      console.log(`✅ Mastery Requirements: ${Object.keys(venomItem.fruitData?.masteryRequirements || {}).length} moves`)
      console.log(`✅ Combat Ratings: ${Object.keys(venomItem.fruitData?.combatRating || {}).length} categories`)
      console.log(`✅ Pros: ${venomItem.fruitData?.pros?.length || 0} advantages`)
      console.log(`✅ Cons: ${venomItem.fruitData?.cons?.length || 0} disadvantages`)
      console.log(`✅ Recommendations: ${venomItem.fruitData?.recommendations?.length || 0} tips`)
      console.log(`✅ Counters: ${venomItem.fruitData?.counters?.length || 0} matchups`)
      console.log(`✅ Trivia: ${venomItem.fruitData?.trivia?.length || 0} facts`)
      console.log(`✅ Change History: ${venomItem.fruitData?.changeHistory?.length || 0} updates`)
      console.log(`✅ Moves: ${venomItem.moves?.length || 0} abilities`)
      
      const totalDataPoints = (venomItem.fruitData?.passiveAbilities?.length || 0) +
                             Object.keys(venomItem.fruitData?.masteryRequirements || {}).length +
                             (venomItem.fruitData?.pros?.length || 0) +
                             (venomItem.fruitData?.cons?.length || 0) +
                             (venomItem.fruitData?.trivia?.length || 0) +
                             (venomItem.moves?.length || 0)
      
      console.log(`\n🎯 TOTAL DATA POINTS EXTRACTED: ${totalDataPoints}`)
      console.log("🎉 Venom fruit analysis completed successfully!")
      
    } else {
      console.log("❌ Failed to scrape Venom fruit")
    }
    
  } catch (error) {
    console.error("❌ Error during Venom analysis:", error)
  }
  
  console.log("\n🏁 Complete Venom analysis finished!")
}

// Exécuter le test
testVenomComplete().catch(console.error)
