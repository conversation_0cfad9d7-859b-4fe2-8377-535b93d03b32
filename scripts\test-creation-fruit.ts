import dotenv from "dotenv"
import { FandomAPIScraper } from "../lib/scrapers/fandom-api-scraper"

// Charger les variables d'environnement
dotenv.config({ path: ".env.local" })

async function testCreationFruit() {
  const mongoUrl = process.env.MONGODB_URL
  if (!mongoUrl) {
    console.error("❌ MONGODB_URL environment variable is required")
    console.log("💡 Create a .env.local file with:")
    console.log("MONGODB_URL=mongodb://localhost:27017/bloxfruits")
    process.exit(1)
  }

  console.log("🔮 TESTING CREATION FRUIT SPECIFICALLY")
  console.log("======================================")
  console.log("Testing Creation fruit to verify Robux price extraction")
  console.log("")

  const scraper = new FandomAPIScraper(mongoUrl)

  try {
    console.log("📄 Testing Creation fruit...")
    const item = await scraper.scrapeItem("Creation", "fruit")
    
    if (item) {
      console.log("✅ Creation: Successfully scraped!")
      console.log("")
      
      // Informations de base
      console.log("📊 BASIC INFORMATION:")
      console.log("====================")
      console.log(`Name: ${item.name}`)
      console.log(`Type: ${item.type}`)
      console.log(`Category: ${item.category}`)
      console.log(`Rarity: ${item.rarity}`)
      console.log("")
      
      // Prix
      console.log("💰 PRICING INFORMATION:")
      console.log("======================")
      console.log(`Beli Price: ${item.price}`)
      console.log(`Robux Price: ${item.robuxPrice}`)
      console.log("")
      
      // Vérifier les données brutes
      if (item.rawData && item.rawData.infobox) {
        console.log("📋 RAW INFOBOX DATA:")
        console.log("===================")
        console.log(`Money: ${item.rawData.infobox.money}`)
        console.log(`Robux: ${item.rawData.infobox.robux}`)
        console.log("")
      }
      
      // Vérifier les données du fruit
      if (item.fruitData) {
        console.log("🍎 FRUIT DATA:")
        console.log("==============")
        console.log(`Type: ${item.fruitData.type}`)
        console.log(`Value: ${item.fruitData.value?.toLocaleString()}`)
        console.log(`Transformation: ${item.fruitData.transformation}`)
        if (item.fruitData.combatRating) {
          console.log(`Combat Rating:`)
          Object.entries(item.fruitData.combatRating).forEach(([key, value]) => {
            console.log(`  ${key}: ${value}`)
          })
        }
        console.log("")
      }
      
      // Vérifier les moves
      if (item.moves && item.moves.length > 0) {
        console.log("⚔️ MOVES:")
        console.log("=========")
        console.log(`Total moves: ${item.moves.length}`)
        item.moves.slice(0, 3).forEach((move, index) => {
          console.log(`${index + 1}. ${move.name} (Mastery: ${move.mastery})`)
        })
        if (item.moves.length > 3) {
          console.log(`... and ${item.moves.length - 3} more moves`)
        }
        console.log("")
      }
      
      // Vérifier les images
      if (item.imageUrls && item.imageUrls.length > 0) {
        console.log("🖼️ IMAGES:")
        console.log("==========")
        console.log(`Total images: ${item.imageUrls.length}`)
        item.imageUrls.slice(0, 2).forEach((url, index) => {
          console.log(`${index + 1}. ${url}`)
        })
        if (item.imageUrls.length > 2) {
          console.log(`... and ${item.imageUrls.length - 2} more images`)
        }
        console.log("")
      }
      
      // Vérifier la trivia
      if (item.fruitData && item.fruitData.trivia && item.fruitData.trivia.length > 0) {
        console.log("📚 TRIVIA:")
        console.log("==========")
        console.log(`Total trivia: ${item.fruitData.trivia.length}`)
        item.fruitData.trivia.slice(0, 2).forEach((trivia, index) => {
          console.log(`${index + 1}. ${trivia.substring(0, 80)}...`)
        })
        console.log("")
      }
      
      // Analyse du prix Robux
      console.log("🔍 ROBUX PRICE ANALYSIS:")
      console.log("========================")
      const extractedRobux = item.robuxPrice ? parseInt(item.robuxPrice.replace(/,/g, '')) : null
      const expectedRobux = 1750 // Prix attendu selon l'infobox
      
      if (extractedRobux === expectedRobux) {
        console.log(`✅ CORRECT: Robux price is accurate (${extractedRobux})`)
      } else if (extractedRobux === null) {
        console.log(`❌ MISSING: No Robux price extracted`)
      } else {
        console.log(`⚠️ INCORRECT: Got ${extractedRobux}, expected ${expectedRobux}`)
      }
      
      // Recommandations
      console.log("")
      console.log("💡 RECOMMENDATIONS:")
      console.log("===================")
      if (extractedRobux === expectedRobux) {
        console.log("✅ Robux price extraction is working correctly for Creation")
        console.log("✅ Data is ready for use in the application")
      } else {
        console.log("⚠️ Robux price extraction needs adjustment")
        console.log("🔧 Check infobox parsing and pattern matching")
      }
      
    } else {
      console.log("❌ Creation: Failed to scrape")
    }

  } catch (error) {
    console.error("❌ Error during Creation fruit testing:", error)
  }
  
  console.log("\n🏁 Creation fruit testing completed!")
}

// Exécuter le test
testCreationFruit().catch(console.error)
