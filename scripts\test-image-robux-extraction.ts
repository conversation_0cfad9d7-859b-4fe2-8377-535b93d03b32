import dotenv from "dotenv"
import { FandomAPIScraper } from "../lib/scrapers/fandom-api-scraper"

// Charger les variables d'environnement
dotenv.config({ path: ".env.local" })

async function testImageRobuxExtraction() {
  const mongoUrl = process.env.MONGODB_URL
  if (!mongoUrl) {
    console.error("❌ MONGODB_URL environment variable is required")
    console.log("💡 Create a .env.local file with:")
    console.log("MONGODB_URL=mongodb://localhost:27017/bloxfruits")
    process.exit(1)
  }

  console.log("🖼️ TESTING IMAGE & ROBUX EXTRACTION")
  console.log("===================================")
  console.log("Testing enhanced scraper with image URLs and Robux prices")
  console.log("")

  const scraper = new FandomAPIScraper(mongoUrl)

  try {
    // Test avec des items qui ont des images et des prix Robux
    const testItems = [
      { name: "Dragon", category: "fruit" },
      { name: "<PERSON><PERSON>", category: "fruit" },
      { name: "Venom", category: "fruit" },
      { name: "Lei", category: "accessory" },
      { name: "Bear Ears", category: "accessory" },
      { name: "Bazooka", category: "gun" },
      { name: "Bisento", category: "sword" },
      { name: "Blaze Ember", category: "material" }
    ]
    
    console.log(`🔍 Testing ${testItems.length} items for image and Robux extraction...`)
    
    for (const testItem of testItems) {
      try {
        console.log(`\n📄 Testing ${testItem.name} (${testItem.category})...`)
        const item = await scraper.scrapeItem(testItem.name, testItem.category)
        
        if (item) {
          console.log(`✅ ${testItem.name}: Successfully scraped!`)
          
          // Vérifier les images
          if (item.imageUrls && item.imageUrls.length > 0) {
            console.log(`   🖼️ Images found (${item.imageUrls.length}):`)
            item.imageUrls.slice(0, 3).forEach((url, index) => {
              console.log(`     ${index + 1}. ${url}`)
            })
            if (item.imageUrls.length > 3) {
              console.log(`     ... and ${item.imageUrls.length - 3} more images`)
            }
          } else if (item.imageUrl) {
            console.log(`   🖼️ Single image: ${item.imageUrl}`)
          } else {
            console.log(`   ⚠️ No images found`)
          }
          
          // Vérifier les prix
          if (item.price) {
            console.log(`   💰 Price: ${item.price}`)
          }
          
          if (item.robuxPrice) {
            console.log(`   💎 Robux Price: ${item.robuxPrice}`)
          }
          
          // Vérifier les données spécialisées améliorées
          if (item.accessoryData) {
            if (item.accessoryData.price) {
              console.log(`   💰 Accessory Price: ${item.accessoryData.price}`)
            }
            if (item.accessoryData.buffs && item.accessoryData.buffs.length > 0) {
              console.log(`   💪 Buffs: ${item.accessoryData.buffs.length} found`)
            }
          }
          
          if (item.materialData) {
            if (item.materialData.usage && item.materialData.usage.length > 0) {
              console.log(`   🔧 Usage categories: ${item.materialData.usage.length}`)
              item.materialData.usage.forEach(usage => {
                console.log(`     - ${usage.type}: ${usage.items.length} items`)
              })
            }
          }
          
          if (item.weaponData) {
            if (item.weaponData.price) {
              console.log(`   💰 Weapon Price: ${item.weaponData.price}`)
            }
            if (item.weaponData.dropChance) {
              console.log(`   🎲 Drop Chance: ${item.weaponData.dropChance}%`)
            }
          }
          
          if (item.fruitData) {
            if (item.fruitData.value) {
              console.log(`   💰 Fruit Value: ${item.fruitData.value.toLocaleString()}`)
            }
          }
          
        } else {
          console.log(`❌ ${testItem.name}: Failed to scrape`)
        }
        
        // Petite pause entre les requêtes
        await new Promise(resolve => setTimeout(resolve, 1000))
        
      } catch (error) {
        console.log(`❌ ${testItem.name}: Error - ${error}`)
      }
    }
    
    console.log("\n📊 EXTRACTION IMPROVEMENTS SUMMARY:")
    console.log("===================================")
    console.log("✅ Image URL extraction from galleries")
    console.log("✅ Multiple image support (imageUrls array)")
    console.log("✅ Robux price extraction from infobox and wikitext")
    console.log("✅ Enhanced accessory data with prices")
    console.log("✅ Improved material usage extraction")
    console.log("✅ Better weapon data with drop chances")
    console.log("✅ Fixed regex errors in template extraction")
    
    console.log("\n🎯 NEW FEATURES TESTED:")
    console.log("=======================")
    console.log("🖼️ Gallery image parsing with proper URL construction")
    console.log("💎 Robux price detection from multiple patterns")
    console.log("🔧 Enhanced usage extraction for materials")
    console.log("💰 Price extraction for all item types")
    console.log("🛡️ Regex escaping to prevent extraction errors")
    console.log("📊 Comprehensive data validation and cleaning")

  } catch (error) {
    console.error("❌ Error during image/Robux testing:", error)
  }
  
  console.log("\n🎉 Image & Robux extraction testing completed!")
}

// Exécuter le test
testImageRobuxExtraction().catch(console.error)
