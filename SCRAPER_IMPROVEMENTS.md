# Améliorations du Scraper Blox Fruits

## 🎯 Vue d'ensemble

Le scraper Fandom API a été considérablement amélioré pour extraire des données plus précises et spécialisées selon le type d'item. Ces améliorations permettent une meilleure utilisation des données dans l'application Blox Fruits Calculator.

## 🔧 Corrections et améliorations critiques

### **🛡️ Correction des erreurs regex**
- **Échappement des caractères spéciaux** : Template names avec caractères spéciaux (parenthèses, crochets)
- **Validation robuste** : Prévention des erreurs "Invalid regular expression"
- **Gestion d'erreurs améliorée** : Recovery automatique pour les patterns malformés

### **🖼️ Extraction des images**
- **URLs multiples** : Support des galleries avec plusieurs images (`imageUrls` array)
- **Construction d'URLs** : URLs Fandom correctement formatées
- **Fallback intelligent** : Image principale + images additionnelles

### **💎 Extraction des prix Robux**
- **Patterns multiples** : {{Robux|X}}, "X robux", "robux: X"
- **Validation numérique** : Parsing intelligent des valeurs
- **Intégration complète** : Disponible pour tous les types d'items

## 🔧 Nouvelles fonctionnalités

### 1. Extraction spécialisée par type d'item

#### 🧪 **Matériaux (Materials)**
- **Types de berries** : Liste complète des différents types de berries disponibles
- **Localisations** : Mapping détaillé des emplacements avec nombre de bushes par mer
- **Utilisation** : Extraction des recettes et items craftables (skins d'aura, dragon, eagle)
- **Totaux requis** : Quantités nécessaires pour débloquer tous les items
- **Propriétés de spawn** : Taux de spawn, temps de despawn, stack maximum

#### 🍎 **Fruits du Démon (Devil Fruits) - ULTRA AMÉLIORÉ**
- **Type de fruit** : Natural, Elemental, Beast, Zoan, Logia, Paramecia
- **Awakening** : Disponibilité de l'éveil
- **Transformation** : Détection automatique des capacités de transformation
- **Capacités passives** : Extraction des passives avec descriptions détaillées (Fury Meter, Hazardous Puddles, etc.)
- **Exigences de maîtrise** : Mapping complet des requirements par move
- **Ratings de combat** : PvP, Grinding, Raids (Excellent/Good/Average/Poor)
- **Pros et Cons** : Listes complètes des avantages et inconvénients
- **Recommandations** : Races et styles de jeu recommandés
- **Counters** : Fruits et stratégies contre lesquels il est efficace
- **Trivia** : Faits intéressants et lore du fruit
- **Historique des changements** : Tracking complet des updates et modifications
- **Valeurs** : Prix en Beli et chances de spawn/stock

#### ⚔️ **Armes (Swords/Guns) - ULTRA AMÉLIORÉ**
- **Type d'arme** : Sword, Gun avec détection automatique
- **Versions** : V1, V2, V3 avec détection des upgrades
- **Statistiques complètes** : Dégâts, cooldown par move depuis les tableaux
- **Prix et drop** : Prix d'achat, chances de drop, level requirements
- **Exigences d'amélioration** : Matériaux et quantités pour les upgrades
- **Pros et Cons** : Avantages et inconvénients détaillés
- **Capacités spéciales** : Abilities passives et actives

#### 💍 **Accessoires - NOUVEAU**
- **Buffs détaillés** : Type, valeur, description (ex: +50% Health Regen)
- **Rareté et source** : Drop source, localisation, chances
- **Compatibilité** : Items avec lesquels ils stackent
- **Pros et Cons** : Analyse des avantages/inconvénients
- **Trivia** : Informations additionnelles et conseils d'utilisation

#### ⚙️ **Mécaniques de jeu - NOUVEAU**
- **Purpose** : Objectif et utilité de la mécanique
- **Déclenchement** : Comment activer/utiliser la mécanique
- **Fonctionnement** : Explication détaillée des mécaniques
- **Restrictions** : Limitations (Marines vs Pirates, etc.)
- **Notes** : Informations importantes et tips

#### 👥 **NPCs**
- **Type de NPC** : Quest, Shop, Misc, Boss, Enemy
- **Localisation** : Mer et emplacement précis
- **Services** : Race Evolution, Shop, Quest Giver
- **Exigences de quête** : Requirements avec montants
- **Étapes de quête** : Steps détaillées par race/type
- **Dialogue** : Phrases et réponses du NPC

#### 🎯 **Quêtes (Quests)**
- **Quest Giver** : NPC qui donne la quête
- **Requirements** : Exigences avec types et montants
- **Étapes** : Steps détaillées de completion
- **Difficulté** : Easy, Medium, Hard, Extreme (auto-calculée)
- **Tips** : Conseils pour compléter la quête
- **Rewards** : Récompenses obtenues

#### ⚔️ **Ennemis/Raids**
- **Type d'ennemi** : Raid, Boss, Regular, Elite
- **Statistiques** : HP, Level, Base Attack
- **Attaques** : Nom, description, comment éviter
- **Immunités** : Types de dégâts immunisés
- **Aura** : Présence d'aura ou non
- **Arme** : Arme utilisée par l'ennemi
- **Comportement** : Patterns d'attaque et stratégies

### 2. Structure de données améliorée

```typescript
interface ScrapedItem {
  // Données de base (existantes)
  name: string
  type: string
  category: string
  rarity?: string
  price?: string
  // ...

  // Nouvelles données spécialisées
  materialData?: {
    berryTypes?: string[]
    locations?: Array<{ sea: string; location: string; bushes: number }>
    usage?: Array<{ type: string; items: Array<{ name: string; rarity: string; price: string }> }>
    totalRequired?: Record<string, number>
    maxStack?: number
    source?: string
    spawnRate?: string
    despawnTime?: string
  }

  fruitData?: {
    awakening?: boolean
    type?: "Natural" | "Elemental" | "Beast" | "Zoan" | "Logia" | "Paramecia"
    value?: number
    stockChance?: number
    spawnChance?: number
  }

  weaponData?: {
    damage?: number
    masteryRequired?: number
    upgradeRequirements?: Array<{ material: string; quantity: number }>
    specialAbilities?: string[]
  }

  npcData?: {
    npcType?: "Quest" | "Shop" | "Misc" | "Boss" | "Enemy"
    sea?: number
    location?: string
    questRequirements?: Array<{ type: string; description: string; amount?: number }>
    questSteps?: string[]
    dialogue?: string[]
    services?: string[]
  }

  questData?: {
    questGiver?: string
    requirements?: Array<{ type: string; description: string; amount?: number }>
    steps?: string[]
    difficulty?: "Easy" | "Medium" | "Hard" | "Extreme"
    tips?: string[]
  }

  enemyData?: {
    enemyType?: "Raid" | "Boss" | "Regular" | "Elite"
    hp?: number
    level?: number
    baseAttack?: number
    attacks?: Array<{ name: string; description: string; howToAvoid: string }>
    immunity?: string[]
    aura?: boolean
    weapon?: string
    behavior?: string
  }
}
```

### 3. Méthodes d'extraction spécialisées

#### `extractMaterialData(wikitext: string)`
- Parse les sections "List of Berries", "Locations", "Usage"
- Extrait les tableaux de données avec regex avancées
- Gère les structures tabber complexes (Aura/Dragon/Eagle skins)
- Calcule les totaux requis depuis les notes

#### `extractFruitData(wikitext: string, infoboxData: Record<string, string>)`
- Validation des types de fruits avec enum strict
- Extraction des valeurs monétaires avec parsing intelligent
- Détection automatique des capacités d'awakening
- Calcul des chances de spawn/stock

#### `extractWeaponData(wikitext: string, infoboxData: Record<string, string>)`
- Parse les sections "Upgrading" pour les matériaux requis
- Extraction des capacités spéciales depuis multiple sections
- Gestion des dégâts et maîtrise avec validation numérique

## 📊 Exemple de données extraites

### Berries (Material)
```json
{
  "name": "Berries",
  "category": "material",
  "rarity": "Common",
  "materialData": {
    "berryTypes": [
      "Blue Icicle Berry",
      "Green Toad Berry", 
      "Orange Berry",
      "Pink Pig Berry",
      "Purple Jelly Berry",
      "Red Cherry Berry",
      "White Cloud Berry",
      "Yellow Star Berry"
    ],
    "locations": [
      { "sea": "First Sea", "location": "Middle Town", "bushes": 6 },
      { "sea": "Second Sea", "location": "Kingdom of Rose", "bushes": 11 },
      { "sea": "Third Sea", "location": "Hydra Island", "bushes": 66 }
    ],
    "usage": [
      {
        "type": "Aura Skin",
        "items": [
          { "name": "Bright Yellow", "rarity": "Rare", "price": "1 Yellow Star Berry + 1,500 Fragments" }
        ]
      }
    ],
    "totalRequired": {
      "Blue Icicle Berry": 30,
      "Red Cherry Berry": 40,
      "White Cloud Berry": 33
    },
    "maxStack": 99,
    "source": "Bushes",
    "spawnRate": "15 minutes",
    "despawnTime": "1 hour"
  }
}
```

### **Venom (Fruit) - DONNÉES AMÉLIORÉES**
```json
{
  "name": "Venom",
  "category": "fruit",
  "rarity": "Mythical",
  "price": "3,000,000",
  "robuxPrice": "2,450",
  "fruitData": {
    "type": "Natural",
    "awakening": false,
    "transformation": true,
    "value": 3000000,
    "passiveAbilities": [
      {
        "name": "Fury Meter",
        "description": "User starts with 50% fury. Needs full meter to transform. Gains 50% defense when transformed."
      },
      {
        "name": "Hazardous Puddles",
        "description": "Creates poisonous puddles that deal tick damage and distort enemy vision."
      },
      {
        "name": "Venomous Drop",
        "description": "Air jumping while transformed creates poison puddles on landing."
      }
    ],
    "masteryRequirements": {
      "Poison Daggers": 1,
      "Noxious Shot": 100,
      "Toxic Fog": 200,
      "Transformation": 300,
      "Serpent's Wrath": 50
    },
    "combatRating": {
      "pvp": "Excellent",
      "grinding": "Good",
      "raids": "Excellent"
    },
    "pros": [
      "All moves break Instinct, excluding [V]",
      "Very versatile, fits almost every playstyle",
      "Extremely good for close-range battles",
      "Puddles drain Instinct dodges",
      "High damage output with lack of knockback"
    ],
    "cons": [
      "Difficult to obtain and expensive",
      "Requires full Fury Meter to transform",
      "Passive Puddles useless while in air",
      "All moves have limited range"
    ],
    "recommendations": [
      "Ghoul V3",
      "Shark V2/V3 passive",
      "Human V3"
    ],
    "counters": [
      "Counters Light",
      "Counters Dough",
      "Counters T-Rex"
    ],
    "trivia": [
      "Venom is the only fruit that starts with the letter V",
      "Despite having transformation, it is not a Beast fruit",
      "Venom is the only Natural fruit to have a Fury Meter",
      "Transformation model was made by Mary (@Marubravery on X)"
    ],
    "changeHistory": [
      {
        "update": "20",
        "changes": ["Venom Fruit's model's icon was remade"]
      },
      {
        "update": "17.2",
        "changes": ["Transformation now costs 20% of the user's Fury Meter"]
      }
    ]
  }
}
```

### **Lei (Accessory) - NOUVELLES DONNÉES**
```json
{
  "name": "Lei",
  "category": "accessory",
  "rarity": "Rare",
  "accessoryData": {
    "buffs": [
      {
        "type": "Percentage",
        "value": "+50%",
        "description": "Health Regen"
      }
    ],
    "rarity": "Rare",
    "dropSource": "Kilo Admiral",
    "location": "Great Tree",
    "pros": [
      "Useful for survivability, helps recover and keep fighting"
    ],
    "cons": [
      "Only increases health regeneration",
      "Not the best for PvP as regeneration gets blocked"
    ],
    "trivia": [
      "Lei is the highest health regeneration accessory in the game",
      "Lei can be useful when paired with other recovery moves"
    ]
  }
}
```

### **Bazooka (Gun) - NOUVELLES DONNÉES**
```json
{
  "name": "Bazooka",
  "category": "gun",
  "weaponData": {
    "weaponType": "Gun",
    "version": 1,
    "dropChance": 10,
    "stats": [
      {
        "move": "Heat Wave",
        "damage": 14,
        "cooldown": 50
      },
      {
        "move": "Flaring Missiles",
        "damage": 22,
        "cooldown": 80
      }
    ],
    "pros": [
      "All moves have AoE on impact",
      "Highest M1 damage in the game",
      "Can break Instinct"
    ],
    "cons": [
      "Rubber users are immune",
      "Terrible for combos",
      "High Mastery requirements"
    ]
  }
}
```

## 🚀 Scripts de test

### `npm run test-material`
Teste l'extraction des données de matériaux avec focus sur les Berries.

### `npm run test-fruit`
Teste l'extraction des données de fruits populaires (Dragon, Leopard, etc.).

### `npm run test-enhanced-fruit`
Teste l'extraction améliorée des fruits avec passives, ratings, pros/cons et transformations.

### `npm run test-venom-complete`
Analyse complète du fruit Venom avec toutes les nouvelles fonctionnalités (trivia, recommendations, counters, change history).

### `npm run test-all-categories`
Test complet de toutes les catégories : Accessories, Weapons (Swords/Guns), Game Mechanics avec extraction spécialisée.

### `npm run test-image-robux`
Test de l'extraction des URLs d'images et des prix Robux pour tous les types d'items.

### `npm run test-regex-fix`
Test spécifique pour vérifier la correction des erreurs regex avec des items problématiques.

### `npm run test-npc-quest-enemy`
Teste l'extraction des données NPCs, Quests et Enemies avec toutes les nouvelles fonctionnalités.

### `npm run test-all-enhanced`
Test complet de toutes les fonctionnalités améliorées avec rapport de succès.

### `npm run scrape-all`
Scraping complet avec toutes les nouvelles catégories : Materials, NPCs, Quests, Enemies.

## 🔄 Intégration avec l'application

### Base de données
- Nouveau schéma MongoDB avec champs spécialisés
- Index optimisés pour les requêtes par type de données
- Support des structures imbriquées pour les données complexes

### API endpoints
Les nouvelles données sont automatiquement disponibles via les endpoints existants :
- `/api/items?category=material` - Matériaux avec données de localisation
- `/api/items?category=fruit` - Fruits avec données d'awakening
- `/api/items?category=sword` - Armes avec données d'upgrade

### Calculateurs
- **Trading Calculator** : Utilise les valeurs de fruits extraites
- **Farming Calculator** : Utilise les données de localisation des matériaux
- **Stats Calculator** : Utilise les données d'armes pour les recommandations

## 📈 Améliorations de performance

1. **Rate limiting intelligent** : Respect des limites API Fandom
2. **Caching des données** : Évite les requêtes redondantes
3. **Parsing optimisé** : Regex compilées et réutilisables
4. **Validation des données** : Filtrage des données invalides
5. **Gestion d'erreurs** : Recovery automatique et logging détaillé

## 🎯 Prochaines étapes

1. **Expansion des catégories** : NPCs, Quests, Raids
2. **Données de trading en temps réel** : Intégration avec sources externes
3. **Machine learning** : Prédiction des tendances de marché
4. **API publique** : Exposition des données pour la communauté
5. **Dashboard admin** : Interface de monitoring du scraping

## 🔧 Utilisation

```bash
# Tester le scraper de matériaux
npm run test-material

# Tester le scraper de fruits
npm run test-fruit

# Tester le scraper de fruits amélioré
npm run test-enhanced-fruit

# Analyse complète du fruit Venom
npm run test-venom-complete

# Test de toutes les catégories (Accessories, Weapons, Mechanics)
npm run test-all-categories

# Test extraction images et prix Robux
npm run test-image-robux

# Test correction erreurs regex
npm run test-regex-fix

# Tester NPCs, Quests et Enemies
npm run test-npc-quest-enemy

# Test complet de toutes les fonctionnalités
npm run test-all-enhanced

# Scraping complet avec toutes les nouvelles fonctionnalités
npm run scrape-all

# Configuration de la base de données
npm run setup-db
```

Ces améliorations transforment le scraper en un outil puissant capable d'extraire des données riches et structurées, essentielles pour le bon fonctionnement de l'application Blox Fruits Calculator.
