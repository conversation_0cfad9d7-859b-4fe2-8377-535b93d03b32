import dotenv from "dotenv"
import { FandomAPIScraper } from "../lib/scrapers/fandom-api-scraper"

// Charger les variables d'environnement
dotenv.config({ path: ".env.local" })

async function testRegexFix() {
  const mongoUrl = process.env.MONGODB_URL
  if (!mongoUrl) {
    console.error("❌ MONGODB_URL environment variable is required")
    console.log("💡 Create a .env.local file with:")
    console.log("MONGODB_URL=mongodb://localhost:27017/bloxfruits")
    process.exit(1)
  }

  console.log("🔧 TESTING REGEX FIX")
  console.log("====================")
  console.log("Testing items that previously caused regex errors")
  console.log("")

  const scraper = new FandomAPIScraper(mongoUrl)

  try {
    // Test avec des items qui causaient des erreurs regex
    const problematicItems = [
      { name: "The Strongest God", category: "npc" },
      { name: "Thunder God", category: "npc" },
      { name: "The Mayor", category: "npc" },
      { name: "The Saw", category: "npc" },
      { name: "Legendary}} [[Guns", category: "gun" }, // Test avec des caractères spéciaux
    ]
    
    console.log(`🔍 Testing ${problematicItems.length} problematic items...`)
    
    let successCount = 0
    let errorCount = 0
    
    for (const testItem of problematicItems) {
      try {
        console.log(`\n📄 Testing ${testItem.name} (${testItem.category})...`)
        const item = await scraper.scrapeItem(testItem.name, testItem.category)
        
        if (item) {
          console.log(`✅ ${testItem.name}: Successfully scraped!`)
          console.log(`   📝 Name: ${item.name}`)
          console.log(`   🏷️ Category: ${item.category}`)
          console.log(`   📊 Fields: ${Object.keys(item).length}`)
          
          if (item.description) {
            console.log(`   📖 Description: ${item.description.substring(0, 80)}...`)
          }
          
          // Vérifier les données spécialisées
          if (item.npcData) {
            console.log(`   👤 NPC Data: Available`)
            if (item.npcData.npcType) {
              console.log(`     🎭 Type: ${item.npcData.npcType}`)
            }
            if (item.npcData.location) {
              console.log(`     📍 Location: ${item.npcData.location}`)
            }
          }
          
          if (item.weaponData) {
            console.log(`   ⚔️ Weapon Data: Available`)
            if (item.weaponData.weaponType) {
              console.log(`     🔫 Type: ${item.weaponData.weaponType}`)
            }
          }
          
          successCount++
        } else {
          console.log(`⚠️ ${testItem.name}: No data returned (but no error)`)
          successCount++
        }
        
        // Petite pause entre les requêtes
        await new Promise(resolve => setTimeout(resolve, 500))
        
      } catch (error) {
        console.log(`❌ ${testItem.name}: Error - ${error}`)
        errorCount++
        
        // Vérifier si c'est encore une erreur regex
        if (error.toString().includes("Invalid regular expression")) {
          console.log(`   🚨 REGEX ERROR STILL PRESENT!`)
        } else {
          console.log(`   ℹ️ Different error type (not regex)`)
        }
      }
    }
    
    console.log("\n📊 REGEX FIX RESULTS:")
    console.log("=====================")
    console.log(`✅ Successful: ${successCount}/${problematicItems.length}`)
    console.log(`❌ Errors: ${errorCount}/${problematicItems.length}`)
    
    if (errorCount === 0) {
      console.log("🎉 ALL REGEX ERRORS FIXED!")
    } else {
      console.log("⚠️ Some errors still present (check logs above)")
    }
    
    console.log("\n🔧 FIXES IMPLEMENTED:")
    console.log("=====================")
    console.log("✅ Template name escaping in extractTemplateData")
    console.log("✅ Special character handling in regex patterns")
    console.log("✅ Robust error handling for malformed templates")
    console.log("✅ Improved pattern matching for complex names")
    
    console.log("\n🎯 ADDITIONAL IMPROVEMENTS:")
    console.log("===========================")
    console.log("🖼️ Image URL extraction from galleries")
    console.log("💎 Robux price extraction")
    console.log("📊 Enhanced data extraction for all categories")
    console.log("🛡️ Better error handling and validation")

  } catch (error) {
    console.error("❌ Error during regex fix testing:", error)
  }
  
  console.log("\n🏁 Regex fix testing completed!")
}

// Exécuter le test
testRegexFix().catch(console.error)
