import dotenv from "dotenv"
import { FandomAPIScraper } from "../lib/scrapers/fandom-api-scraper"

// Charger les variables d'environnement
dotenv.config({ path: ".env.local" })

async function diagnosticScraper() {
  const mongoUrl = process.env.MONGODB_URL
  if (!mongoUrl) {
    console.error("❌ MONGODB_URL environment variable is required")
    console.log("💡 Create a .env.local file with:")
    console.log("MONGODB_URL=mongodb://localhost:27017/bloxfruits")
    process.exit(1)
  }

  console.log("🔍 DIAGNOSTIC COMPLET DU SCRAPER")
  console.log("================================")
  console.log("Vérification globale des anomalies et problèmes")
  console.log("")

  const scraper = new FandomAPIScraper(mongoUrl)

  try {
    // Test des items problématiques identifiés
    const diagnosticItems = [
      { 
        name: "Items", 
        category: "quest",
        issues: ["Mal catégorisé comme fruit", "Devrait être info"],
        tests: ["category", "type", "infobox"]
      },
      { 
        name: "Saber", 
        category: "sword",
        issues: ["Pros/cons mélangés", "Moves manquants", "Stats incomplètes"],
        tests: ["moves", "pros", "cons", "stats"]
      },
      { 
        name: "Aura Editor", 
        category: "mechanic",
        issues: ["Devrait être NPC", "mechanicData vide"],
        tests: ["category", "npcData", "locations"]
      },
      { 
        name: "Creation", 
        category: "fruit",
        issues: ["Prix Robux inconsistant"],
        tests: ["robuxPrice", "infobox", "price"]
      }
    ]
    
    console.log(`🔍 Diagnostic de ${diagnosticItems.length} items problématiques...`)
    
    let totalIssues = 0
    let resolvedIssues = 0
    
    for (const item of diagnosticItems) {
      try {
        console.log(`\n📄 DIAGNOSTIC: ${item.name} (${item.category})`)
        console.log(`   🚨 Issues connues: ${item.issues.join(', ')}`)
        
        const scrapedItem = await scraper.scrapeItem(item.name, item.category)
        
        if (scrapedItem) {
          console.log(`   ✅ Scraping réussi`)
          
          // Test de catégorisation
          if (item.tests.includes("category")) {
            totalIssues++
            console.log(`   📂 Catégorie détectée: ${scrapedItem.category}`)
            console.log(`   🏷️ Type détecté: ${scrapedItem.type}`)
            
            if (item.name === "Items" && scrapedItem.category === "info") {
              console.log(`   ✅ RÉSOLU: Catégorisation corrigée`)
              resolvedIssues++
            } else if (item.name === "Aura Editor" && scrapedItem.category === "npc") {
              console.log(`   ✅ RÉSOLU: Catégorisation corrigée`)
              resolvedIssues++
            } else if (item.name === "Items" && scrapedItem.category === "fruit") {
              console.log(`   ❌ PROBLÈME: Toujours mal catégorisé`)
            }
          }
          
          // Test des moves
          if (item.tests.includes("moves")) {
            totalIssues++
            const movesCount = scrapedItem.moves ? scrapedItem.moves.length : 0
            console.log(`   ⚔️ Moves extraits: ${movesCount}`)
            
            if (item.name === "Saber" && movesCount > 0) {
              console.log(`   ✅ RÉSOLU: Moves extraits`)
              resolvedIssues++
              scrapedItem.moves?.slice(0, 2).forEach(move => {
                console.log(`     - ${move.name} (Mastery: ${move.mastery})`)
              })
            } else if (item.name === "Saber" && movesCount === 0) {
              console.log(`   ❌ PROBLÈME: Aucun move extrait`)
            }
          }
          
          // Test des pros/cons
          if (item.tests.includes("pros") || item.tests.includes("cons")) {
            totalIssues++
            const prosCount = scrapedItem.weaponData?.pros?.length || 0
            const consCount = scrapedItem.weaponData?.cons?.length || 0
            console.log(`   👍 Pros: ${prosCount}, 👎 Cons: ${consCount}`)
            
            if (prosCount > 0 && consCount > 0) {
              // Vérifier s'il y a des doublons entre pros et cons
              const pros = scrapedItem.weaponData?.pros || []
              const cons = scrapedItem.weaponData?.cons || []
              const duplicates = pros.filter(pro => cons.includes(pro))
              
              if (duplicates.length > 0) {
                console.log(`   ❌ PROBLÈME: ${duplicates.length} doublons pros/cons`)
                duplicates.slice(0, 2).forEach(dup => {
                  console.log(`     - "${dup.substring(0, 50)}..."`)
                })
              } else {
                console.log(`   ✅ RÉSOLU: Pros/cons séparés correctement`)
                resolvedIssues++
              }
            }
          }
          
          // Test des stats
          if (item.tests.includes("stats")) {
            totalIssues++
            const statsCount = scrapedItem.weaponData?.stats?.length || 0
            console.log(`   📊 Stats extraites: ${statsCount}`)
            
            if (statsCount > 0) {
              console.log(`   ✅ RÉSOLU: Stats extraites`)
              resolvedIssues++
              scrapedItem.weaponData?.stats?.slice(0, 2).forEach(stat => {
                console.log(`     - ${stat.move}: ${stat.damage} dmg, ${stat.cooldown}s`)
              })
            }
          }
          
          // Test du prix Robux
          if (item.tests.includes("robuxPrice")) {
            totalIssues++
            console.log(`   💎 Prix Robux: ${scrapedItem.robuxPrice || 'N/A'}`)
            
            if (scrapedItem.rawData?.infobox?.robux) {
              console.log(`   📋 Infobox Robux: ${scrapedItem.rawData.infobox.robux}`)
              
              if (scrapedItem.robuxPrice === scrapedItem.rawData.infobox.robux.replace(/,/g, '')) {
                console.log(`   ✅ RÉSOLU: Prix Robux cohérent`)
                resolvedIssues++
              } else {
                console.log(`   ❌ PROBLÈME: Prix Robux incohérent`)
              }
            }
          }
          
          // Test des données NPC
          if (item.tests.includes("npcData")) {
            totalIssues++
            if (scrapedItem.npcData && Object.keys(scrapedItem.npcData).length > 0) {
              console.log(`   👤 NPC Data: Présent`)
              if (scrapedItem.npcData.locations) {
                console.log(`     📍 Locations: ${scrapedItem.npcData.locations.join(', ')}`)
              }
              if (scrapedItem.npcData.services) {
                console.log(`     🔧 Services: ${scrapedItem.npcData.services.join(', ')}`)
              }
              console.log(`   ✅ RÉSOLU: NPC Data extrait`)
              resolvedIssues++
            } else {
              console.log(`   ❌ PROBLÈME: NPC Data manquant`)
            }
          }
          
          // Test de l'infobox
          if (item.tests.includes("infobox")) {
            totalIssues++
            const infoboxKeys = Object.keys(scrapedItem.rawData?.infobox || {})
            console.log(`   📋 Infobox: ${infoboxKeys.length} champs`)
            
            if (infoboxKeys.length > 0) {
              console.log(`   ✅ RÉSOLU: Infobox extrait`)
              resolvedIssues++
            } else {
              console.log(`   ❌ PROBLÈME: Infobox vide`)
            }
          }
          
        } else {
          console.log(`   ❌ ÉCHEC: Scraping échoué`)
        }
        
        await new Promise(resolve => setTimeout(resolve, 1500))
        
      } catch (error) {
        console.log(`   ❌ ERREUR: ${error}`)
      }
    }
    
    console.log("\n📊 RÉSULTATS DU DIAGNOSTIC:")
    console.log("===========================")
    console.log(`✅ Issues résolues: ${resolvedIssues}/${totalIssues}`)
    
    const resolutionRate = totalIssues > 0 ? ((resolvedIssues / totalIssues) * 100).toFixed(1) : 0
    console.log(`📈 Taux de résolution: ${resolutionRate}%`)
    
    if (resolutionRate >= 80) {
      console.log("🎉 EXCELLENT! La plupart des problèmes sont résolus!")
    } else if (resolutionRate >= 60) {
      console.log("👍 BIEN! Plusieurs problèmes sont résolus")
    } else {
      console.log("⚠️ ATTENTION! Plusieurs problèmes persistent")
    }
    
    console.log("\n🔧 PROBLÈMES IDENTIFIÉS:")
    console.log("========================")
    console.log("🔍 Catégorisation automatique (Items → info)")
    console.log("⚔️ Extraction de moves avec tabbers (Saber)")
    console.log("👍👎 Séparation pros/cons (doublons)")
    console.log("💎 Cohérence prix Robux (infobox vs extrait)")
    console.log("👤 Détection NPC vs mechanic")
    
    console.log("\n💡 RECOMMANDATIONS:")
    console.log("===================")
    if (resolutionRate < 80) {
      console.log("🔧 Affiner la logique de détection de catégorie")
      console.log("📝 Améliorer l'extraction des moves avec tabbers")
      console.log("🧹 Nettoyer la logique pros/cons pour éviter les doublons")
      console.log("💎 Standardiser l'extraction des prix Robux")
    } else {
      console.log("✅ Le scraper fonctionne bien globalement")
      console.log("🔍 Continuer les tests sur d'autres items")
    }

  } catch (error) {
    console.error("❌ Erreur durant le diagnostic:", error)
  }
  
  console.log("\n🏁 Diagnostic complet terminé!")
}

// Exécuter le diagnostic
diagnosticScraper().catch(console.error)
