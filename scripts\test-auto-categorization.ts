import dotenv from "dotenv"
import { FandomAPIScraper } from "../lib/scrapers/fandom-api-scraper"

// Charger les variables d'environnement
dotenv.config({ path: ".env.local" })

async function testAutoCategorization() {
  const mongoUrl = process.env.MONGODB_URL
  if (!mongoUrl) {
    console.error("❌ MONGODB_URL environment variable is required")
    console.log("💡 Create a .env.local file with:")
    console.log("MONGODB_URL=mongodb://localhost:27017/bloxfruits")
    process.exit(1)
  }

  console.log("🔄 TESTING AUTO-CATEGORIZATION")
  console.log("==============================")
  console.log("Testing automatic category detection and correction")
  console.log("")

  const scraper = new FandomAPIScraper(mongoUrl)

  try {
    // Test avec des items mal catégorisés
    const testItems = [
      { 
        name: "Aura Editor", 
        originalCategory: "mechanic",
        expectedCategory: "npc",
        expectedData: {
          type: "Misc",
          locations: ["Middle Town", "Kingdom of Rose", "Castle on the Sea"],
          services: ["Appearance Editor"]
        }
      },
      { 
        name: "Arctic Warrior", 
        originalCategory: "npc",
        expectedCategory: "enemy",
        expectedData: {
          type: "Enemy",
          level: 1350,
          hp: 12550,
          location: "Ice Castle"
        }
      },
      { 
        name: "Items", 
        originalCategory: "quest",
        expectedCategory: "info",
        expectedData: {
          type: "Information Page",
          isActualQuest: false
        }
      },
      { 
        name: "Shocker", 
        originalCategory: "raid",
        expectedCategory: "enemy",
        expectedData: {
          type: "Enemy",
          hp: 8300,
          location: "Raids"
        }
      }
    ]
    
    console.log(`🔍 Testing ${testItems.length} items with categorization issues...`)
    
    let correctCategorizations = 0
    let totalTests = testItems.length
    
    for (const testItem of testItems) {
      try {
        console.log(`\n📄 Testing ${testItem.name}...`)
        console.log(`   📂 Original category: ${testItem.originalCategory}`)
        console.log(`   🎯 Expected category: ${testItem.expectedCategory}`)
        
        const item = await scraper.scrapeItem(testItem.name, testItem.originalCategory)
        
        if (item) {
          console.log(`   ✅ Successfully scraped!`)
          console.log(`   📊 Detected category: ${item.category}`)
          console.log(`   🏷️ Detected type: ${item.type}`)
          
          // Vérifier si la catégorie a été corrigée
          if (item.category === testItem.expectedCategory) {
            console.log(`   🎉 CORRECT: Category auto-corrected!`)
            correctCategorizations++
            
            // Vérifier les données spécialisées selon la catégorie
            if (item.category === "npc" && item.npcData) {
              console.log(`   👤 NPC Data found:`)
              if (item.npcData.locations) {
                console.log(`     📍 Locations: ${item.npcData.locations.join(', ')}`)
              }
              if (item.npcData.services) {
                console.log(`     🔧 Services: ${item.npcData.services.join(', ')}`)
              }
            }
            
            if (item.category === "enemy" && item.enemyData) {
              console.log(`   👹 Enemy Data found:`)
              if (item.enemyData.level) {
                console.log(`     📊 Level: ${item.enemyData.level}`)
              }
              if (item.enemyData.hp) {
                console.log(`     ❤️ HP: ${item.enemyData.hp.toLocaleString()}`)
              }
              if (item.enemyData.spawnLocation) {
                console.log(`     📍 Location: ${item.enemyData.spawnLocation.join(', ')}`)
              }
            }
            
          } else {
            console.log(`   ❌ INCORRECT: Category not corrected (got ${item.category}, expected ${testItem.expectedCategory})`)
          }
          
          // Vérifier les images
          if (item.imageUrls && item.imageUrls.length > 0) {
            console.log(`   🖼️ Images: ${item.imageUrls.length} found`)
          }
          
        } else {
          console.log(`   ❌ Failed to scrape`)
        }
        
        await new Promise(resolve => setTimeout(resolve, 1500))
        
      } catch (error) {
        console.log(`   ❌ Error: ${error}`)
      }
    }
    
    console.log("\n📊 AUTO-CATEGORIZATION RESULTS:")
    console.log("===============================")
    console.log(`✅ Correct categorizations: ${correctCategorizations}/${totalTests}`)
    
    const accuracy = totalTests > 0 ? ((correctCategorizations / totalTests) * 100).toFixed(1) : 0
    console.log(`📈 Accuracy: ${accuracy}%`)
    
    if (accuracy >= 90) {
      console.log("🎉 EXCELLENT! Auto-categorization is working perfectly!")
    } else if (accuracy >= 70) {
      console.log("👍 GOOD! Auto-categorization is mostly working")
    } else {
      console.log("⚠️ NEEDS IMPROVEMENT: Auto-categorization needs refinement")
    }
    
    console.log("\n🔧 CATEGORIZATION RULES TESTED:")
    console.log("===============================")
    console.log("✅ Enemy detection: {{Enemy Infobox}}, HP, level, 'are Lv.' patterns")
    console.log("✅ NPC detection: {{NPC Infobox}}, 'Misc NPC', Quest Giver patterns")
    console.log("✅ Info page detection: 'are objects', 'Types of', no {{Quest}} patterns")
    console.log("✅ Weapon detection: {{Weapon Infobox}}, type Sword/Gun")
    console.log("✅ Fruit detection: {{Blox Fruit Infobox}}, Robux prices")
    
    console.log("\n💡 IMPROVEMENTS MADE:")
    console.log("====================")
    console.log("✅ Automatic category detection based on content analysis")
    console.log("✅ Multiple location support for NPCs (location1, location2, etc.)")
    console.log("✅ Service detection for NPCs (Editor, Quest Giver, Shop, etc.)")
    console.log("✅ Better enemy vs NPC distinction")
    console.log("✅ Info page vs actual quest distinction")
    
    console.log("\n🎯 NEXT STEPS:")
    console.log("==============")
    console.log("✅ Use corrected categories for better data organization")
    console.log("✅ Implement category-specific UI components")
    console.log("✅ Add category filters in search functionality")
    console.log("✅ Create specialized views for each category")

  } catch (error) {
    console.error("❌ Error during auto-categorization testing:", error)
  }
  
  console.log("\n🏁 Auto-categorization testing completed!")
}

// Exécuter le test
testAutoCategorization().catch(console.error)
