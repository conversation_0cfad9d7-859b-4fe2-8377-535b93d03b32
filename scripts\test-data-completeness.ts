import dotenv from "dotenv"
import { FandomAPIScraper } from "../lib/scrapers/fandom-api-scraper"

// Charger les variables d'environnement
dotenv.config({ path: ".env.local" })

async function testDataCompleteness() {
  const mongoUrl = process.env.MONGODB_URL
  if (!mongoUrl) {
    console.error("❌ MONGODB_URL environment variable is required")
    console.log("💡 Create a .env.local file with:")
    console.log("MONGODB_URL=mongodb://localhost:27017/bloxfruits")
    process.exit(1)
  }

  console.log("📊 TESTING DATA COMPLETENESS")
  console.log("============================")
  console.log("Testing improved data extraction for all categories")
  console.log("")

  const scraper = new FandomAPIScraper(mongoUrl)

  try {
    // Test avec les items qui avaient des problèmes
    const testItems = [
      { 
        name: "D.S. Coat", 
        category: "accessory",
        expectedData: {
          rarity: "Legendary",
          buffs: ["5% Damage", "200 Energy", "200 Health"],
          source: "Admins"
        }
      },
      { 
        name: "Cake Queen", 
        category: "enemy",
        expectedData: {
          level: 2175,
          hp: 260875,
          location: "Ice Cream Land",
          attacks: ["Heat Slash", "Lightning Wave"]
        }
      },
      { 
        name: "Musket", 
        category: "gun",
        expectedData: {
          price: "8,000",
          rarity: "Uncommon",
          moves: ["Draconic Bullet", "Torching Shot"],
          stats: true
        }
      },
      { 
        name: "Azure Ember", 
        category: "material",
        expectedData: {
          rarity: "Legendary",
          usage: ["Kitsune Shrine", "Upgrading"],
          maxStack: 25
        }
      }
    ]
    
    console.log(`🔍 Testing ${testItems.length} items with known issues...`)
    
    let totalImprovements = 0
    let totalTests = 0
    
    for (const testItem of testItems) {
      try {
        console.log(`\n📄 Testing ${testItem.name} (${testItem.category})...`)
        const item = await scraper.scrapeItem(testItem.name, testItem.category)
        
        if (item) {
          console.log(`✅ ${testItem.name}: Successfully scraped!`)
          
          // Test des améliorations spécifiques
          if (testItem.category === "accessory") {
            console.log(`   🏷️ Testing accessory data...`)
            
            // Test rarity
            totalTests++
            if (item.rarity || (item.accessoryData && item.accessoryData.rarity)) {
              const rarity = item.rarity || item.accessoryData?.rarity
              console.log(`     ✅ Rarity: ${rarity}`)
              if (rarity === testItem.expectedData.rarity) totalImprovements++
            } else {
              console.log(`     ❌ Rarity: Missing`)
            }
            
            // Test buffs
            totalTests++
            if (item.accessoryData && item.accessoryData.buffs && item.accessoryData.buffs.length > 0) {
              console.log(`     ✅ Buffs: ${item.accessoryData.buffs.length} found`)
              item.accessoryData.buffs.forEach(buff => {
                console.log(`       - ${buff.value} ${buff.description}`)
              })
              totalImprovements++
            } else {
              console.log(`     ❌ Buffs: Missing or incomplete`)
            }
          }
          
          if (testItem.category === "enemy") {
            console.log(`   👹 Testing enemy data...`)
            
            // Test level
            totalTests++
            if (item.level || (item.enemyData && item.enemyData.level)) {
              const level = item.level || item.enemyData?.level
              console.log(`     ✅ Level: ${level}`)
              if (level === testItem.expectedData.level) totalImprovements++
            } else {
              console.log(`     ❌ Level: Missing`)
            }
            
            // Test HP
            totalTests++
            if (item.enemyData && item.enemyData.hp) {
              console.log(`     ✅ HP: ${item.enemyData.hp.toLocaleString()}`)
              if (item.enemyData.hp === testItem.expectedData.hp) totalImprovements++
            } else {
              console.log(`     ❌ HP: Missing`)
            }
            
            // Test location
            totalTests++
            if (item.enemyData && item.enemyData.spawnLocation) {
              console.log(`     ✅ Location: ${item.enemyData.spawnLocation.join(', ')}`)
              totalImprovements++
            } else {
              console.log(`     ❌ Location: Missing`)
            }
          }
          
          if (testItem.category === "gun") {
            console.log(`   🔫 Testing weapon data...`)
            
            // Test price
            totalTests++
            if (item.price || (item.weaponData && item.weaponData.price)) {
              const price = item.price || item.weaponData?.price
              console.log(`     ✅ Price: ${price}`)
              totalImprovements++
            } else {
              console.log(`     ❌ Price: Missing`)
            }
            
            // Test moves
            totalTests++
            if (item.moves && item.moves.length > 0) {
              console.log(`     ✅ Moves: ${item.moves.length} found`)
              item.moves.forEach(move => {
                console.log(`       - ${move.name} (Mastery: ${move.mastery})`)
              })
              totalImprovements++
            } else {
              console.log(`     ❌ Moves: Missing`)
            }
            
            // Test stats
            totalTests++
            if (item.weaponData && item.weaponData.stats && item.weaponData.stats.length > 0) {
              console.log(`     ✅ Stats: ${item.weaponData.stats.length} moves`)
              totalImprovements++
            } else {
              console.log(`     ❌ Stats: Missing`)
            }
          }
          
          if (testItem.category === "material") {
            console.log(`   🧱 Testing material data...`)
            
            // Test usage
            totalTests++
            if (item.materialData && item.materialData.usage && item.materialData.usage.length > 0) {
              console.log(`     ✅ Usage: ${item.materialData.usage.length} categories`)
              item.materialData.usage.forEach(usage => {
                console.log(`       - ${usage.type}: ${usage.items.length} items`)
              })
              totalImprovements++
            } else {
              console.log(`     ❌ Usage: Missing or empty`)
            }
          }
          
          // Test images
          totalTests++
          if (item.imageUrls && item.imageUrls.length > 0) {
            console.log(`   🖼️ Images: ${item.imageUrls.length} found`)
            totalImprovements++
          } else {
            console.log(`   ❌ Images: Missing`)
          }
          
        } else {
          console.log(`❌ ${testItem.name}: Failed to scrape`)
        }
        
        await new Promise(resolve => setTimeout(resolve, 1500))
        
      } catch (error) {
        console.log(`❌ ${testItem.name}: Error - ${error}`)
      }
    }
    
    console.log("\n📊 DATA COMPLETENESS RESULTS:")
    console.log("=============================")
    console.log(`✅ Improvements working: ${totalImprovements}/${totalTests}`)
    
    const improvementRate = totalTests > 0 ? ((totalImprovements / totalTests) * 100).toFixed(1) : 0
    console.log(`📈 Improvement rate: ${improvementRate}%`)
    
    if (improvementRate >= 80) {
      console.log("🎉 EXCELLENT! Data extraction improvements are working well!")
    } else if (improvementRate >= 60) {
      console.log("👍 GOOD! Most improvements are working")
    } else {
      console.log("⚠️ NEEDS WORK: Some improvements need refinement")
    }
    
    console.log("\n🔧 IMPROVEMENTS TESTED:")
    console.log("======================")
    console.log("✅ Accessory rarity extraction from wikitext")
    console.log("✅ Enhanced buff parsing (percentage + numeric)")
    console.log("✅ Enemy HP, location, and attacks extraction")
    console.log("✅ Weapon price extraction from {{Money}} templates")
    console.log("✅ Material usage extraction from multiple sections")
    console.log("✅ Real image URLs from Fandom pages")

  } catch (error) {
    console.error("❌ Error during data completeness testing:", error)
  }
  
  console.log("\n🏁 Data completeness testing completed!")
}

// Exécuter le test
testDataCompleteness().catch(console.error)
