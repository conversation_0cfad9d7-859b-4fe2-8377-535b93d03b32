import dotenv from "dotenv"
import { MongoClient } from "mongodb"

// Charger les variables d'environnement
dotenv.config({ path: ".env.local" })

async function testDatabaseConnection() {
  const mongoUrl = process.env.MONGODB_URL
  if (!mongoUrl) {
    console.error("❌ MONGODB_URL environment variable is required")
    process.exit(1)
  }

  console.log("🔍 TEST DE CONNEXION À LA BASE DE DONNÉES")
  console.log("========================================")
  console.log(`📡 URL: ${mongoUrl.replace(/\/\/.*@/, '//***:***@')}`)

  const client = new MongoClient(mongoUrl)

  try {
    console.log("🔌 Connexion à MongoDB...")
    await client.connect()
    console.log("✅ Connexion réussie!")

    const db = client.db("bloxfruits")
    console.log("📊 Base de données: bloxfruits")

    // Lister les collections
    console.log("\n📂 COLLECTIONS DISPONIBLES:")
    const collections = await db.listCollections().toArray()
    console.log(`Nombre de collections: ${collections.length}`)

    for (const collection of collections) {
      const count = await db.collection(collection.name).countDocuments()
      console.log(`   - ${collection.name}: ${count} documents`)
      
      if (count > 0) {
        // Échantillon de la première entrée
        const sample = await db.collection(collection.name).findOne({})
        const fields = Object.keys(sample || {})
        console.log(`     Champs: ${fields.slice(0, 5).join(', ')}${fields.length > 5 ? '...' : ''}`)
      }
    }

    // Test de requête simple
    console.log("\n🔍 TEST DE REQUÊTE:")
    const mainCollections = ['items', 'fruits', 'swords', 'guns', 'npcs', 'enemies']
    
    for (const collectionName of mainCollections) {
      try {
        const collection = db.collection(collectionName)
        const count = await collection.countDocuments()
        if (count > 0) {
          console.log(`✅ ${collectionName}: ${count} documents`)
          
          // Échantillon avec catégories
          const sample = await collection.findOne({})
          if (sample && 'category' in sample) {
            const categories = await collection.distinct('category')
            console.log(`   Catégories: ${categories.join(', ')}`)
          }
        }
      } catch (error) {
        console.log(`❌ ${collectionName}: Erreur - ${error}`)
      }
    }

  } catch (error) {
    console.error("❌ Erreur de connexion:", error)
  } finally {
    await client.close()
    console.log("\n🔌 Connexion fermée")
  }

  console.log("\n🏁 Test de connexion terminé!")
}

// Exécuter le test
testDatabaseConnection().catch(console.error)
