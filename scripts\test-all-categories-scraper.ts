import dotenv from "dotenv"
import { FandomAPIScraper } from "../lib/scrapers/fandom-api-scraper"

// Charger les variables d'environnement
dotenv.config({ path: ".env.local" })

async function testAllCategoriesScraper() {
  const mongoUrl = process.env.MONGODB_URL
  if (!mongoUrl) {
    console.error("❌ MONGODB_URL environment variable is required")
    console.log("💡 Create a .env.local file with:")
    console.log("MONGODB_URL=mongodb://localhost:27017/bloxfruits")
    process.exit(1)
  }

  console.log("🌟 COMPREHENSIVE CATEGORY TESTING")
  console.log("=================================")
  console.log("Testing all enhanced scraper categories with specialized data extraction")
  console.log("")

  const scraper = new FandomAPIScraper(mongoUrl)

  try {
    // Test Accessories
    console.log("💍 TESTING ACCESSORIES")
    console.log("======================")
    
    const testAccessories = ["Lei", "Ghoul Mask", "Swordsman Hat"]
    
    for (const accessory of testAccessories) {
      try {
        console.log(`\n📄 Testing ${accessory} accessory...`)
        const item = await scraper.scrapeItem(accessory, "accessory")
        
        if (item && item.accessoryData) {
          console.log(`✅ ${accessory}: Accessory data extracted`)
          
          if (item.accessoryData.buffs && item.accessoryData.buffs.length > 0) {
            console.log(`   💪 Buffs (${item.accessoryData.buffs.length}):`)
            item.accessoryData.buffs.slice(0, 2).forEach(buff => {
              console.log(`     - ${buff.type}: ${buff.value} ${buff.description}`)
            })
          }
          
          if (item.accessoryData.rarity) {
            console.log(`   ⭐ Rarity: ${item.accessoryData.rarity}`)
          }
          
          if (item.accessoryData.dropSource) {
            console.log(`   🎯 Drop Source: ${item.accessoryData.dropSource}`)
          }
          
          if (item.accessoryData.pros && item.accessoryData.pros.length > 0) {
            console.log(`   ✅ Pros: ${item.accessoryData.pros.length} advantages`)
          }
          
          if (item.accessoryData.cons && item.accessoryData.cons.length > 0) {
            console.log(`   ❌ Cons: ${item.accessoryData.cons.length} disadvantages`)
          }
          
        } else {
          console.log(`⚠️ ${accessory}: No accessory data`)
        }
        
        await new Promise(resolve => setTimeout(resolve, 1000))
        
      } catch (error) {
        console.log(`❌ ${accessory}: Error - ${error}`)
      }
    }

    // Test Weapons (Guns and Swords)
    console.log("\n\n⚔️ TESTING WEAPONS")
    console.log("==================")
    
    const testWeapons = [
      { name: "Bazooka", category: "gun" },
      { name: "Bisento", category: "sword" },
      { name: "Katana", category: "sword" }
    ]
    
    for (const weapon of testWeapons) {
      try {
        console.log(`\n📄 Testing ${weapon.name} ${weapon.category}...`)
        const item = await scraper.scrapeItem(weapon.name, weapon.category)
        
        if (item && item.weaponData) {
          console.log(`✅ ${weapon.name}: Weapon data extracted`)
          
          if (item.weaponData.weaponType) {
            console.log(`   🔫 Type: ${item.weaponData.weaponType}`)
          }
          
          if (item.weaponData.price) {
            console.log(`   💰 Price: ${item.weaponData.price.toLocaleString()}`)
          }
          
          if (item.weaponData.dropChance) {
            console.log(`   🎲 Drop Chance: ${item.weaponData.dropChance}%`)
          }
          
          if (item.weaponData.version) {
            console.log(`   📊 Version: V${item.weaponData.version}`)
          }
          
          if (item.weaponData.stats && item.weaponData.stats.length > 0) {
            console.log(`   📈 Stats (${item.weaponData.stats.length} moves):`)
            item.weaponData.stats.slice(0, 2).forEach(stat => {
              console.log(`     - ${stat.move}: ${stat.damage} damage, ${stat.cooldown}s cooldown`)
            })
          }
          
          if (item.weaponData.pros && item.weaponData.pros.length > 0) {
            console.log(`   ✅ Pros: ${item.weaponData.pros.length} advantages`)
          }
          
          if (item.weaponData.cons && item.weaponData.cons.length > 0) {
            console.log(`   ❌ Cons: ${item.weaponData.cons.length} disadvantages`)
          }
          
        } else {
          console.log(`⚠️ ${weapon.name}: No weapon data`)
        }
        
        await new Promise(resolve => setTimeout(resolve, 1000))
        
      } catch (error) {
        console.log(`❌ ${weapon.name}: Error - ${error}`)
      }
    }

    // Test Mechanics
    console.log("\n\n⚙️ TESTING GAME MECHANICS")
    console.log("=========================")
    
    const testMechanics = ["Allying", "Instinct", "Bounty"]
    
    for (const mechanic of testMechanics) {
      try {
        console.log(`\n📄 Testing ${mechanic} mechanic...`)
        const item = await scraper.scrapeItem(mechanic, "mechanic")
        
        if (item && item.mechanicData) {
          console.log(`✅ ${mechanic}: Mechanic data extracted`)
          
          if (item.mechanicData.purpose) {
            console.log(`   🎯 Purpose: ${item.mechanicData.purpose}`)
          }
          
          if (item.mechanicData.triggeredBy) {
            console.log(`   🔄 Triggered By: ${item.mechanicData.triggeredBy}`)
          }
          
          if (item.mechanicData.mechanics && item.mechanicData.mechanics.length > 0) {
            console.log(`   ⚙️ Mechanics (${item.mechanicData.mechanics.length}):`)
            item.mechanicData.mechanics.slice(0, 2).forEach(mech => {
              console.log(`     - ${mech.substring(0, 80)}...`)
            })
          }
          
          if (item.mechanicData.notes && item.mechanicData.notes.length > 0) {
            console.log(`   📝 Notes: ${item.mechanicData.notes.length} items`)
          }
          
          if (item.mechanicData.restrictions && item.mechanicData.restrictions.length > 0) {
            console.log(`   🚫 Restrictions: ${item.mechanicData.restrictions.length} items`)
          }
          
        } else {
          console.log(`⚠️ ${mechanic}: No mechanic data`)
        }
        
        await new Promise(resolve => setTimeout(resolve, 1000))
        
      } catch (error) {
        console.log(`❌ ${mechanic}: Error - ${error}`)
      }
    }

    console.log("\n📊 COMPREHENSIVE TESTING SUMMARY:")
    console.log("==================================")
    console.log("✅ Accessories: Buffs, rarity, drop sources, pros/cons")
    console.log("✅ Weapons: Type, stats, versions, upgrade requirements")
    console.log("✅ Game Mechanics: Purpose, triggers, restrictions, notes")
    console.log("✅ Enhanced data extraction for all categories")
    console.log("✅ Structured data storage in MongoDB")
    
    console.log("\n🎯 NEW FEATURES TESTED:")
    console.log("=======================")
    console.log("🔍 Accessory buff extraction with percentages")
    console.log("📊 Weapon stats tables with damage/cooldown")
    console.log("⚙️ Game mechanic purpose and trigger detection")
    console.log("📈 Version detection for weapons (V1, V2, V3)")
    console.log("💰 Price and drop chance extraction")
    console.log("✅ Comprehensive pros/cons analysis")
    console.log("📚 Trivia and additional information")

  } catch (error) {
    console.error("❌ Error during comprehensive testing:", error)
  }
  
  console.log("\n🎉 Comprehensive category testing completed!")
}

// Exécuter le test
testAllCategoriesScraper().catch(console.error)
