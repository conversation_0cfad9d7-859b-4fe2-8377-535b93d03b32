import dotenv from "dotenv"
import { FandomAPIScraper } from "../lib/scrapers/fandom-api-scraper"

// Charger les variables d'environnement
dotenv.config({ path: ".env.local" })

async function testRealImages() {
  const mongoUrl = process.env.MONGODB_URL
  if (!mongoUrl) {
    console.error("❌ MONGODB_URL environment variable is required")
    console.log("💡 Create a .env.local file with:")
    console.log("MONGODB_URL=mongodb://localhost:27017/bloxfruits")
    process.exit(1)
  }

  console.log("🖼️ TESTING REAL IMAGE EXTRACTION")
  console.log("=================================")
  console.log("Testing extraction of actual image URLs from Fandom pages")
  console.log("")

  const scraper = new FandomAPIScraper(mongoUrl)

  try {
    // Test avec des items qui ont des images connues
    const testItems = [
      { name: "Dragon", category: "fruit", expectedImages: ["icon", "transformation", "moves"] },
      { name: "Venom", category: "fruit", expectedImages: ["icon", "transformation", "abilities"] },
      { name: "Lei", category: "accessory", expectedImages: ["icon", "in-game"] },
      { name: "Bazooka", category: "gun", expectedImages: ["icon", "equipped", "held", "aiming"] },
      { name: "Bisento", category: "sword", expectedImages: ["icon", "equipped", "held"] },
      { name: "Bear Ears", category: "accessory", expectedImages: ["icon", "in-game"] }
    ]
    
    console.log(`🔍 Testing ${testItems.length} items for real image extraction...`)
    
    let totalImages = 0
    let successfulItems = 0
    
    for (const testItem of testItems) {
      try {
        console.log(`\n📄 Testing ${testItem.name} (${testItem.category})...`)
        const item = await scraper.scrapeItem(testItem.name, testItem.category)
        
        if (item) {
          console.log(`✅ ${testItem.name}: Successfully scraped!`)
          
          // Vérifier les images
          if (item.imageUrls && item.imageUrls.length > 0) {
            console.log(`   🖼️ Real Images found (${item.imageUrls.length}):`)
            item.imageUrls.forEach((url, index) => {
              console.log(`     ${index + 1}. ${url}`)
              
              // Vérifier si l'URL semble valide
              if (url.includes('static.wikia.nocookie.net') || url.includes('vignette.wikia.nocookie.net')) {
                console.log(`        ✅ Valid Fandom URL`)
              } else {
                console.log(`        ⚠️ Unexpected URL format`)
              }
            })
            
            totalImages += item.imageUrls.length
            successfulItems++
            
            // Test de validation des URLs (optionnel - peut être lent)
            if (item.imageUrls.length > 0) {
              console.log(`   🔍 Testing first image URL...`)
              try {
                const response = await fetch(item.imageUrls[0], { method: 'HEAD' })
                if (response.ok) {
                  console.log(`        ✅ Image accessible (${response.status})`)
                  console.log(`        📏 Content-Type: ${response.headers.get('content-type')}`)
                } else {
                  console.log(`        ❌ Image not accessible (${response.status})`)
                }
              } catch (error) {
                console.log(`        ❌ Error testing image: ${error}`)
              }
            }
            
          } else if (item.imageUrl) {
            console.log(`   🖼️ Single image: ${item.imageUrl}`)
            totalImages += 1
          } else {
            console.log(`   ⚠️ No images found`)
          }
          
          // Vérifier les autres données importantes
          if (item.robuxPrice) {
            console.log(`   💎 Robux Price: ${item.robuxPrice}`)
          }
          
          if (item.price) {
            console.log(`   💰 Price: ${item.price}`)
          }
          
        } else {
          console.log(`❌ ${testItem.name}: Failed to scrape`)
        }
        
        // Pause plus longue pour éviter de surcharger le serveur
        await new Promise(resolve => setTimeout(resolve, 2000))
        
      } catch (error) {
        console.log(`❌ ${testItem.name}: Error - ${error}`)
      }
    }
    
    console.log("\n📊 REAL IMAGE EXTRACTION RESULTS:")
    console.log("=================================")
    console.log(`✅ Items with images: ${successfulItems}/${testItems.length}`)
    console.log(`🖼️ Total images extracted: ${totalImages}`)
    console.log(`📈 Average images per item: ${(totalImages / testItems.length).toFixed(1)}`)
    
    if (successfulItems > 0) {
      console.log("\n🎉 SUCCESS! Real image extraction is working!")
    } else {
      console.log("\n⚠️ No images extracted - check implementation")
    }
    
    console.log("\n🔧 EXTRACTION METHODS USED:")
    console.log("===========================")
    console.log("1️⃣ MediaWiki API imageinfo query")
    console.log("2️⃣ HTML parsing from page source")
    console.log("3️⃣ Gallery and infobox image extraction")
    console.log("4️⃣ Multiple fallback patterns")
    
    console.log("\n💡 IMAGE URL FORMATS EXPECTED:")
    console.log("==============================")
    console.log("📸 static.wikia.nocookie.net/blox-fruits/images/...")
    console.log("📸 vignette.wikia.nocookie.net/blox-fruits/images/...")
    console.log("📸 Direct file URLs from MediaWiki API")
    
    console.log("\n🎯 NEXT STEPS:")
    console.log("==============")
    console.log("✅ Use extracted images in UI galleries")
    console.log("✅ Cache image URLs for better performance")
    console.log("✅ Validate image accessibility")
    console.log("✅ Implement image optimization")

  } catch (error) {
    console.error("❌ Error during real image testing:", error)
  }
  
  console.log("\n🏁 Real image extraction testing completed!")
}

// Exécuter le test
testRealImages().catch(console.error)
