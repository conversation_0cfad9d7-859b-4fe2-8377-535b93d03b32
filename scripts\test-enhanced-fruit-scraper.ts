import dotenv from "dotenv"
import { FandomAPIScraper } from "../lib/scrapers/fandom-api-scraper"

// Charger les variables d'environnement
dotenv.config({ path: ".env.local" })

async function testEnhancedFruitScraper() {
  const mongoUrl = process.env.MONGODB_URL
  if (!mongoUrl) {
    console.error("❌ MONGODB_URL environment variable is required")
    console.log("💡 Create a .env.local file with:")
    console.log("MONGODB_URL=mongodb://localhost:27017/bloxfruits")
    process.exit(1)
  }

  console.log("🍎 TESTING ENHANCED FRUIT SCRAPER")
  console.log("=================================")
  console.log("Testing enhanced fruit data extraction with detailed analysis")
  console.log("")

  const scraper = new FandomAPIScraper(mongoUrl)

  try {
    // Test avec des fruits populaires qui ont des transformations et des données riches
    const testFruits = [
      "Venom",      // Transformation + passives
      "Dragon",     // Mythical avec awakening
      "Buddha",     // Transformation + grinding
      "Dough",      // Awakening + PvP
      "Leopard",    // Mythical + transformation
      "Shadow",     // Logia + awakening
      "Control",    // Paramecia + unique abilities
      "Spirit",     // Beast + transformation
      "Kitsune",    // Natural + transformation
      "Mammoth"     // Zoan + transformation
    ]
    
    console.log(`🔍 Testing ${testFruits.length} fruits with enhanced extraction...`)
    
    for (const fruitName of testFruits) {
      try {
        console.log(`\n📄 Testing ${fruitName} fruit...`)
        const fruitItem = await scraper.scrapeItem(fruitName, "fruit")
        
        if (fruitItem) {
          console.log(`✅ ${fruitName}: Successfully scraped!`)
          
          // Afficher les informations de base
          console.log(`   📝 Name: ${fruitItem.name}`)
          console.log(`   🏷️ Category: ${fruitItem.category}`)
          console.log(`   ⭐ Rarity: ${fruitItem.rarity || "Unknown"}`)
          console.log(`   💰 Price: ${fruitItem.price || "Unknown"}`)
          console.log(`   💎 Robux Price: ${fruitItem.robuxPrice || "N/A"}`)
          
          if (fruitItem.description) {
            console.log(`   📖 Description: ${fruitItem.description.substring(0, 100)}...`)
          }
          
          // Afficher les données spécifiques aux fruits améliorées
          if (fruitItem.fruitData) {
            console.log(`   🍎 ENHANCED FRUIT DATA:`)
            
            if (fruitItem.fruitData.type) {
              console.log(`     🔥 Type: ${fruitItem.fruitData.type}`)
            }
            
            if (fruitItem.fruitData.awakening !== undefined) {
              console.log(`     ⚡ Awakening: ${fruitItem.fruitData.awakening ? "Available" : "Not Available"}`)
            }
            
            if (fruitItem.fruitData.transformation !== undefined) {
              console.log(`     🔄 Transformation: ${fruitItem.fruitData.transformation ? "Yes" : "No"}`)
            }
            
            if (fruitItem.fruitData.value) {
              console.log(`     💰 Value: ${fruitItem.fruitData.value.toLocaleString()}`)
            }
            
            if (fruitItem.fruitData.passiveAbilities && fruitItem.fruitData.passiveAbilities.length > 0) {
              console.log(`     🌟 Passive Abilities (${fruitItem.fruitData.passiveAbilities.length}):`)
              fruitItem.fruitData.passiveAbilities.slice(0, 3).forEach(passive => {
                console.log(`       - ${passive.name}`)
                console.log(`         ${passive.description.substring(0, 80)}...`)
              })
              if (fruitItem.fruitData.passiveAbilities.length > 3) {
                console.log(`       ... and ${fruitItem.fruitData.passiveAbilities.length - 3} more`)
              }
            }
            
            if (fruitItem.fruitData.masteryRequirements && Object.keys(fruitItem.fruitData.masteryRequirements).length > 0) {
              console.log(`     🎯 Mastery Requirements:`)
              Object.entries(fruitItem.fruitData.masteryRequirements).slice(0, 5).forEach(([move, mastery]) => {
                console.log(`       - ${move}: ${mastery}`)
              })
            }
            
            if (fruitItem.fruitData.combatRating) {
              console.log(`     ⚔️ Combat Ratings:`)
              if (fruitItem.fruitData.combatRating.pvp) {
                console.log(`       🥊 PvP: ${fruitItem.fruitData.combatRating.pvp}`)
              }
              if (fruitItem.fruitData.combatRating.grinding) {
                console.log(`       🌾 Grinding: ${fruitItem.fruitData.combatRating.grinding}`)
              }
              if (fruitItem.fruitData.combatRating.raids) {
                console.log(`       🏴‍☠️ Raids: ${fruitItem.fruitData.combatRating.raids}`)
              }
            }
            
            if (fruitItem.fruitData.pros && fruitItem.fruitData.pros.length > 0) {
              console.log(`     ✅ Pros (${fruitItem.fruitData.pros.length}):`)
              fruitItem.fruitData.pros.slice(0, 3).forEach(pro => {
                console.log(`       + ${pro.substring(0, 70)}...`)
              })
              if (fruitItem.fruitData.pros.length > 3) {
                console.log(`       ... and ${fruitItem.fruitData.pros.length - 3} more pros`)
              }
            }
            
            if (fruitItem.fruitData.cons && fruitItem.fruitData.cons.length > 0) {
              console.log(`     ❌ Cons (${fruitItem.fruitData.cons.length}):`)
              fruitItem.fruitData.cons.slice(0, 3).forEach(con => {
                console.log(`       - ${con.substring(0, 70)}...`)
              })
              if (fruitItem.fruitData.cons.length > 3) {
                console.log(`       ... and ${fruitItem.fruitData.cons.length - 3} more cons`)
              }
            }
          }
          
          // Afficher les moves si disponibles
          if (fruitItem.moves && fruitItem.moves.length > 0) {
            console.log(`   ⚔️ Moves (${fruitItem.moves.length}):`)
            fruitItem.moves.slice(0, 3).forEach(move => {
              console.log(`     - ${move.name}${move.key ? ` [${move.key}]` : ""}`)
              if (move.damage) console.log(`       💥 Damage: ${move.damage}`)
              if (move.cooldown) console.log(`       ⏰ Cooldown: ${move.cooldown}s`)
              if (move.mastery) console.log(`       🎯 Mastery: ${move.mastery}`)
            })
            if (fruitItem.moves.length > 3) {
              console.log(`     ... and ${fruitItem.moves.length - 3} more moves`)
            }
          }
          
        } else {
          console.log(`❌ ${fruitName}: Failed to scrape`)
        }
        
        // Petite pause entre les requêtes
        await new Promise(resolve => setTimeout(resolve, 1500))
        
      } catch (error) {
        console.log(`❌ ${fruitName}: Error - ${error}`)
      }
    }
    
    console.log("\n📊 ENHANCED FEATURES SUMMARY:")
    console.log("=============================")
    console.log("✅ Basic fruit information (name, rarity, price, type)")
    console.log("✅ Awakening and transformation detection")
    console.log("✅ Passive abilities extraction with descriptions")
    console.log("✅ Mastery requirements per move")
    console.log("✅ Combat ratings (PvP, Grinding, Raids)")
    console.log("✅ Comprehensive pros and cons lists")
    console.log("✅ Enhanced move data with keys and stats")
    console.log("✅ Structured data for advanced analysis")
    
    console.log("\n🎯 DATA QUALITY IMPROVEMENTS:")
    console.log("=============================")
    console.log("🔍 Passive abilities parsed from complex tables")
    console.log("📊 Combat effectiveness automatically rated")
    console.log("⚖️ Pros/cons extracted for balanced analysis")
    console.log("🎮 Transformation capabilities detected")
    console.log("📈 Mastery progression mapped per ability")
    console.log("🏆 Multi-category performance ratings")

  } catch (error) {
    console.error("❌ Error during enhanced fruit testing:", error)
  }
  
  console.log("\n🎉 Enhanced fruit scraper testing completed!")
}

// Exécuter le test
testEnhancedFruitScraper().catch(console.error)
