import dotenv from "dotenv"
import { FandomAPIScraper } from "../lib/scrapers/fandom-api-scraper"

// Charger les variables d'environnement
dotenv.config({ path: ".env.local" })

async function testFixes() {
  const mongoUrl = process.env.MONGODB_URL
  if (!mongoUrl) {
    console.error("❌ MONGODB_URL environment variable is required")
    console.log("💡 Create a .env.local file with:")
    console.log("MONGODB_URL=mongodb://localhost:27017/bloxfruits")
    process.exit(1)
  }

  console.log("🔧 TEST DES CORRECTIONS")
  console.log("=======================")
  console.log("Vérification des corrections apportées au scraper")
  console.log("")

  const scraper = new FandomAPIScraper(mongoUrl)

  try {
    // Test des corrections spécifiques
    const testCases = [
      {
        name: "Items",
        category: "quest",
        expectedCategory: "info",
        description: "Test de la correction de catégorisation"
      },
      {
        name: "<PERSON>ber",
        category: "sword",
        expectedCategory: "sword",
        description: "Test de l'extraction des moves avec tabbers"
      },
      {
        name: "Aura Editor",
        category: "mechanic",
        expectedCategory: "npc",
        description: "Test de la détection NPC vs mechanic"
      }
    ]
    
    console.log(`🔍 Test de ${testCases.length} corrections...`)
    
    for (const testCase of testCases) {
      try {
        console.log(`\n📄 TEST: ${testCase.name}`)
        console.log(`   📝 ${testCase.description}`)
        console.log(`   📂 Catégorie originale: ${testCase.category}`)
        console.log(`   🎯 Catégorie attendue: ${testCase.expectedCategory}`)
        
        const item = await scraper.scrapeItem(testCase.name, testCase.category)
        
        if (item) {
          console.log(`   ✅ Scraping réussi`)
          console.log(`   📊 Catégorie détectée: ${item.category}`)
          console.log(`   🏷️ Type: ${item.type}`)
          
          // Vérification spécifique selon le test
          if (testCase.name === "Items") {
            if (item.category === "info") {
              console.log(`   🎉 SUCCÈS: Catégorisation corrigée!`)
            } else {
              console.log(`   ❌ ÉCHEC: Toujours catégorisé comme ${item.category}`)
              
              // Debug: afficher des infos sur pourquoi ça échoue
              console.log(`   🔍 Debug info:`)
              console.log(`     - Description: ${item.description?.substring(0, 100)}...`)
              if (item.rawData?.fullWikitextSample) {
                const hasBloxFruit = item.rawData.fullWikitextSample.includes("Blox Fruit")
                const hasObjects = item.rawData.fullWikitextSample.includes("are objects")
                const hasQuest = item.rawData.fullWikitextSample.includes("{{Quest")
                console.log(`     - Contains "Blox Fruit": ${hasBloxFruit}`)
                console.log(`     - Contains "are objects": ${hasObjects}`)
                console.log(`     - Contains "{{Quest": ${hasQuest}`)
              }
            }
          }
          
          if (testCase.name === "Saber") {
            const movesCount = item.moves?.length || 0
            console.log(`   ⚔️ Moves extraits: ${movesCount}`)
            
            if (movesCount > 0) {
              console.log(`   🎉 SUCCÈS: Moves extraits!`)
              item.moves?.slice(0, 3).forEach(move => {
                console.log(`     - ${move.name} (Mastery: ${move.mastery})`)
              })
            } else {
              console.log(`   ❌ ÉCHEC: Aucun move extrait`)
            }
            
            // Vérifier les pros/cons
            const prosCount = item.weaponData?.pros?.length || 0
            const consCount = item.weaponData?.cons?.length || 0
            console.log(`   👍 Pros: ${prosCount}, 👎 Cons: ${consCount}`)
            
            if (prosCount > 0 && consCount > 0) {
              // Vérifier les doublons
              const pros = item.weaponData?.pros || []
              const cons = item.weaponData?.cons || []
              const duplicates = pros.filter(pro => cons.includes(pro))
              
              if (duplicates.length === 0) {
                console.log(`   🎉 SUCCÈS: Pros/cons séparés correctement!`)
              } else {
                console.log(`   ❌ ÉCHEC: ${duplicates.length} doublons trouvés`)
                duplicates.slice(0, 2).forEach(dup => {
                  console.log(`     - "${dup.substring(0, 40)}..."`)
                })
              }
            }
          }
          
          if (testCase.name === "Aura Editor") {
            if (item.category === "npc") {
              console.log(`   🎉 SUCCÈS: Catégorisation corrigée!`)
              
              if (item.npcData) {
                console.log(`   👤 NPC Data trouvé:`)
                if (item.npcData.locations) {
                  console.log(`     📍 Locations: ${item.npcData.locations.join(', ')}`)
                }
                if (item.npcData.services) {
                  console.log(`     🔧 Services: ${item.npcData.services.join(', ')}`)
                }
              }
            } else {
              console.log(`   ❌ ÉCHEC: Toujours catégorisé comme ${item.category}`)
            }
          }
          
          // Vérifier les images
          if (item.imageUrls && item.imageUrls.length > 0) {
            console.log(`   🖼️ Images: ${item.imageUrls.length} trouvées`)
          }
          
        } else {
          console.log(`   ❌ ÉCHEC: Scraping échoué`)
        }
        
        await new Promise(resolve => setTimeout(resolve, 1500))
        
      } catch (error) {
        console.log(`   ❌ ERREUR: ${error}`)
      }
    }
    
    console.log("\n📊 RÉSUMÉ DES TESTS:")
    console.log("===================")
    console.log("🔧 Corrections testées:")
    console.log("  ✅ Catégorisation automatique améliorée")
    console.log("  ✅ Extraction de moves avec support des tabbers")
    console.log("  ✅ Séparation pros/cons améliorée")
    console.log("  ✅ Détection NPC vs mechanic")
    
    console.log("\n💡 PROCHAINES ÉTAPES:")
    console.log("=====================")
    console.log("🔍 Exécuter le diagnostic complet: npm run diagnostic-scraper")
    console.log("📊 Tester la qualité des données: npm run test-data-completeness")
    console.log("🔄 Tester la catégorisation: npm run test-auto-categorization")
    
    console.log("\n🎯 OBJECTIFS:")
    console.log("=============")
    console.log("✅ 95%+ de précision dans la catégorisation")
    console.log("✅ 90%+ de complétude des données extraites")
    console.log("✅ 0% de doublons dans pros/cons")
    console.log("✅ 100% d'extraction des moves disponibles")

  } catch (error) {
    console.error("❌ Erreur durant les tests:", error)
  }
  
  console.log("\n🏁 Tests des corrections terminés!")
}

// Exécuter les tests
testFixes().catch(console.error)
