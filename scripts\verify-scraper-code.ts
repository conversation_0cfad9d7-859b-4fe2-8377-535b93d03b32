import fs from 'fs'
import path from 'path'

interface CodeIssue {
  type: 'inconsistency' | 'unused' | 'duplicate' | 'logic_error'
  severity: 'high' | 'medium' | 'low'
  file: string
  line?: number
  description: string
  suggestion?: string
}

async function verifyScraperCode() {
  console.log("🔍 VÉRIFICATION GLOBALE DU CODE SCRAPER")
  console.log("======================================")
  console.log("Analyse statique du code pour identifier les incohérences")
  console.log("")

  const issues: CodeIssue[] = []
  const scraperPath = path.join(process.cwd(), 'lib/scrapers/fandom-api-scraper.ts')
  
  if (!fs.existsSync(scraperPath)) {
    console.error("❌ Fichier scraper non trouvé:", scraperPath)
    return
  }

  const scraperCode = fs.readFileSync(scraperPath, 'utf-8')
  const lines = scraperCode.split('\n')

  console.log(`📄 Analyse de ${lines.length} lignes de code...`)

  // 1. Vérifier les incohérences de nommage
  console.log("\n🔍 VÉRIFICATION DES INCOHÉRENCES DE NOMMAGE:")
  
  // Chercher les utilisations de 'category' vs 'actualCategory'
  lines.forEach((line, index) => {
    if (line.includes('category ===') && !line.includes('actualCategory ===')) {
      if (line.includes('sword') || line.includes('gun') || line.includes('fruit')) {
        issues.push({
          type: 'inconsistency',
          severity: 'high',
          file: 'fandom-api-scraper.ts',
          line: index + 1,
          description: `Utilisation de 'category' au lieu de 'actualCategory'`,
          suggestion: `Remplacer 'category' par 'actualCategory'`
        })
      }
    }
  })

  // 2. Vérifier les méthodes non utilisées
  console.log("\n🔍 VÉRIFICATION DES MÉTHODES NON UTILISÉES:")
  
  const methodDefinitions = []
  const methodUsages = []
  
  lines.forEach((line, index) => {
    // Trouver les définitions de méthodes
    const methodDefMatch = line.match(/private\s+(\w+)\s*\(/)
    if (methodDefMatch) {
      methodDefinitions.push({
        name: methodDefMatch[1],
        line: index + 1
      })
    }
    
    // Trouver les utilisations de méthodes
    const methodUseMatch = line.match(/this\.(\w+)\s*\(/)
    if (methodUseMatch) {
      methodUsages.push(methodUseMatch[1])
    }
  })
  
  // Identifier les méthodes non utilisées
  methodDefinitions.forEach(method => {
    if (!methodUsages.includes(method.name)) {
      issues.push({
        type: 'unused',
        severity: 'medium',
        file: 'fandom-api-scraper.ts',
        line: method.line,
        description: `Méthode '${method.name}' définie mais jamais utilisée`,
        suggestion: `Supprimer la méthode ou l'utiliser`
      })
    }
  })

  // 3. Vérifier les duplications de logique
  console.log("\n🔍 VÉRIFICATION DES DUPLICATIONS:")
  
  // Chercher les patterns dupliqués
  const patterns = [
    'infoboxData.type === "Sword"',
    'infoboxData.type === "Gun"',
    'wikitext.includes("{{Weapon Infobox")',
    'actualCategory === "sword"',
    'actualCategory === "gun"'
  ]
  
  patterns.forEach(pattern => {
    const occurrences = []
    lines.forEach((line, index) => {
      if (line.includes(pattern)) {
        occurrences.push(index + 1)
      }
    })
    
    if (occurrences.length > 2) {
      issues.push({
        type: 'duplicate',
        severity: 'medium',
        file: 'fandom-api-scraper.ts',
        description: `Pattern '${pattern}' dupliqué ${occurrences.length} fois`,
        suggestion: `Factoriser la logique commune`
      })
    }
  })

  // 4. Vérifier les erreurs de logique potentielles
  console.log("\n🔍 VÉRIFICATION DES ERREURS DE LOGIQUE:")
  
  lines.forEach((line, index) => {
    // Vérifier les conditions qui pourraient être problématiques
    if (line.includes('wikitext.includes("enemies")') && !line.includes('!wikitext.includes')) {
      issues.push({
        type: 'logic_error',
        severity: 'high',
        file: 'fandom-api-scraper.ts',
        line: index + 1,
        description: `Détection d'ennemi trop large avec 'enemies'`,
        suggestion: `Ajouter des conditions plus spécifiques`
      })
    }
    
    // Vérifier les regex sans flags globaux
    if (line.includes('.match(/') && !line.includes('/g') && line.includes('while')) {
      issues.push({
        type: 'logic_error',
        severity: 'medium',
        file: 'fandom-api-scraper.ts',
        line: index + 1,
        description: `Regex sans flag global dans une boucle while`,
        suggestion: `Ajouter le flag 'g' ou utiliser une autre approche`
      })
    }
  })

  // 5. Vérifier les interfaces et types
  console.log("\n🔍 VÉRIFICATION DES INTERFACES:")
  
  // Chercher les définitions d'interfaces
  const interfaces = []
  let currentInterface = null
  
  lines.forEach((line, index) => {
    if (line.includes('interface ') && line.includes('{')) {
      currentInterface = {
        name: line.match(/interface\s+(\w+)/)?.[1],
        startLine: index + 1,
        properties: []
      }
    }
    
    if (currentInterface && line.includes(':') && !line.includes('//')) {
      const propMatch = line.match(/(\w+)\??:\s*(.+)/)
      if (propMatch) {
        currentInterface.properties.push({
          name: propMatch[1],
          type: propMatch[2].replace(/[,;]/g, '').trim()
        })
      }
    }
    
    if (currentInterface && line.includes('}') && !line.includes('{')) {
      interfaces.push(currentInterface)
      currentInterface = null
    }
  })

  // Afficher les résultats
  console.log("\n📊 RÉSULTATS DE LA VÉRIFICATION:")
  console.log("===============================")
  
  const issuesByType = {
    inconsistency: issues.filter(i => i.type === 'inconsistency'),
    unused: issues.filter(i => i.type === 'unused'),
    duplicate: issues.filter(i => i.type === 'duplicate'),
    logic_error: issues.filter(i => i.type === 'logic_error')
  }
  
  console.log(`🚨 Incohérences: ${issuesByType.inconsistency.length}`)
  console.log(`🗑️ Code non utilisé: ${issuesByType.unused.length}`)
  console.log(`📋 Duplications: ${issuesByType.duplicate.length}`)
  console.log(`⚠️ Erreurs de logique: ${issuesByType.logic_error.length}`)
  
  console.log(`\n📈 Total: ${issues.length} problèmes détectés`)

  // Afficher les problèmes par priorité
  const highPriorityIssues = issues.filter(i => i.severity === 'high')
  const mediumPriorityIssues = issues.filter(i => i.severity === 'medium')
  
  if (highPriorityIssues.length > 0) {
    console.log("\n🚨 PROBLÈMES HAUTE PRIORITÉ:")
    highPriorityIssues.forEach((issue, index) => {
      console.log(`   ${index + 1}. ${issue.description}`)
      if (issue.line) console.log(`      Ligne: ${issue.line}`)
      if (issue.suggestion) console.log(`      💡 ${issue.suggestion}`)
    })
  }
  
  if (mediumPriorityIssues.length > 0) {
    console.log("\n⚠️ PROBLÈMES PRIORITÉ MOYENNE:")
    mediumPriorityIssues.slice(0, 5).forEach((issue, index) => {
      console.log(`   ${index + 1}. ${issue.description}`)
      if (issue.suggestion) console.log(`      💡 ${issue.suggestion}`)
    })
    
    if (mediumPriorityIssues.length > 5) {
      console.log(`   ... et ${mediumPriorityIssues.length - 5} autres`)
    }
  }

  // Statistiques du code
  console.log("\n📊 STATISTIQUES DU CODE:")
  console.log("========================")
  console.log(`📄 Lignes de code: ${lines.length}`)
  console.log(`🔧 Méthodes définies: ${methodDefinitions.length}`)
  console.log(`📋 Interfaces trouvées: ${interfaces.length}`)
  
  interfaces.forEach(iface => {
    console.log(`   - ${iface.name}: ${iface.properties.length} propriétés`)
  })

  // Recommandations
  console.log("\n💡 RECOMMANDATIONS:")
  console.log("==================")
  
  if (highPriorityIssues.length > 0) {
    console.log("🚨 URGENT: Corriger les problèmes haute priorité")
    console.log("   - Incohérences category vs actualCategory")
    console.log("   - Erreurs de logique dans la détection")
  }
  
  if (issuesByType.unused.length > 0) {
    console.log("🧹 NETTOYAGE: Supprimer le code non utilisé")
    console.log("   - Méthodes extractKeyFromText, extractMoveStats, etc.")
  }
  
  if (issuesByType.duplicate.length > 0) {
    console.log("🔄 REFACTORING: Factoriser les duplications")
    console.log("   - Logique de détection d'armes")
    console.log("   - Patterns de validation de types")
  }
  
  console.log("\n🎯 OBJECTIFS:")
  console.log("=============")
  console.log("✅ 0 problème haute priorité")
  console.log("✅ < 5 problèmes priorité moyenne")
  console.log("✅ Code propre et maintenable")
  console.log("✅ Logique cohérente et prévisible")

  console.log("\n🏁 Vérification du code terminée!")
  
  return issues
}

// Exécuter la vérification
verifyScraperCode().catch(console.error)
